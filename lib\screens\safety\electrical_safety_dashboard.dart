import 'package:flutter/material.dart';
import '../../design_system/app_theme.dart';
import '../../design_system/components/reusable_components.dart';

class ElectricalSafetyDashboard extends StatelessWidget {
  const ElectricalSafetyDashboard({super.key});

  static String routeName = 'electrical-safety-dashboard';
  static String routePath = '/safety/electrical-safety-dashboard';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Electrical Safety Dashboard'),
        backgroundColor: AppTheme.primaryNavy,
        foregroundColor: AppTheme.white,
      ),
      body: const Padding(
        padding: EdgeInsets.all(AppTheme.spacingMd),
        child: <PERSON><PERSON>n(
          children: [
            JJE<PERSON>yState(
              title: 'Safety Dashboard',
              subtitle: 'Electrical safety features coming soon',
              icon: Icons.security,
            ),
          ],
        ),
      ),
    );
  }
}
