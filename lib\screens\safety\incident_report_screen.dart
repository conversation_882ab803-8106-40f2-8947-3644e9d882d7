import 'package:flutter/material.dart';
import '../../design_system/app_theme.dart';
import '../../design_system/components/reusable_components.dart';

class IncidentReportScreen extends StatelessWidget {
  const IncidentReportScreen({super.key});

  static String routeName = 'incident-report';
  static String routePath = '/safety/incident-report';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Incident Report'),
        backgroundColor: AppTheme.primaryNavy,
        foregroundColor: AppTheme.white,
      ),
      body: const Padding(
        padding: EdgeInsets.all(AppTheme.spacingMd),
        child: Column(
          children: [
            JJE<PERSON><PERSON>State(
              title: 'Incident Report',
              subtitle: 'Report safety incidents and near misses',
              icon: Icons.report_problem,
            ),
          ],
        ),
      ),
    );
  }
}
