import 'package:flutter/material.dart';
import '../../design_system/app_theme.dart';
import '../../design_system/components/reusable_components.dart';

class PPESuppliersScreen extends StatelessWidget {
  const PPESuppliersScreen({super.key});

  static String routeName = 'ppe-suppliers';
  static String routePath = '/resources/ppe-suppliers';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('PPE Suppliers'),
        backgroundColor: AppTheme.primaryNavy,
        foregroundColor: AppTheme.white,
      ),
      body: const Padding(
        padding: EdgeInsets.all(AppTheme.spacingMd),
        child: Column(
          children: [
            JJEmptyState(
              title: 'PPE Suppliers',
              subtitle: 'Find personal protective equipment suppliers',
              icon: Icons.shield,
            ),
          ],
        ),
      ),
    );
  }
}
