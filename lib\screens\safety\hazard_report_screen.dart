import 'package:flutter/material.dart';
import '../../design_system/app_theme.dart';
import '../../design_system/components/reusable_components.dart';

class HazardReportScreen extends StatelessWidget {
  const HazardReportScreen({super.key});

  static String routeName = 'hazard-report';
  static String routePath = '/safety/hazard-report';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Hazard Report'),
        backgroundColor: AppTheme.primaryNavy,
        foregroundColor: AppTheme.white,
      ),
      body: const Padding(
        padding: EdgeInsets.all(AppTheme.spacingMd),
        child: Column(
          children: [
            JJEmptyState(
              title: 'Hazard Report',
              subtitle: 'Report workplace hazards and unsafe conditions',
              icon: Icons.warning,
            ),
          ],
        ),
      ),
    );
  }
}
