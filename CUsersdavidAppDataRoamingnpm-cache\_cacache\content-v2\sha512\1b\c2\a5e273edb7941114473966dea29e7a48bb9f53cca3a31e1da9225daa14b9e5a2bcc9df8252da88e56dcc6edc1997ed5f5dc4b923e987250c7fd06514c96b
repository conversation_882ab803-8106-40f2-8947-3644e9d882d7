{"_id": "path-scurry", "_rev": "27-1086018190c292848bf3f3f06a13f54f", "name": "path-scurry", "dist-tags": {"latest": "2.0.0"}, "versions": {"0.0.0-0": {"name": "path-scurry", "version": "0.0.0-0", "_id": "path-scurry@0.0.0-0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "a333b7273817228a9a9fef465fe9fcb859cfb94b", "tarball": "https://registry.npmjs.org/path-scurry/-/path-scurry-0.0.0-0.tgz", "fileCount": 1, "integrity": "sha512-wCxrK3Hp56ga8b3ATLuiz8tPcIQ/Yd5a5j8f0vpzbdydQcz3+Awf8zTtxkZTWtBQjAqoHV/lXF1M5k41IFxcjA==", "signatures": [{"sig": "MEYCIQD0TDt2d/tfox11LjaX6JSBjxntfAxbSiEeRZxZxKwteQIhAPKigWMqlN9wL8g2qp1gDjB7BNm1Vn5tB7s1z0AwOgZi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4dwkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqpNQ//b5B975hBzkotF18pcFPWqdN/D2QUXTZuFLCemUHcl15fNfGx\r\ngUeeF3eLNY0xR6KrbbJTDassE+oZPD1AtQDfVJUGoXp6cAwtxh0km3ZRdO1c\r\nHzqscxp+n1mKrpteMBVmShfApilL5ig7dq/Q8u1iJzsIIT0y2L2FZnHPqvYU\r\nrkuM2YFbGu93Lnh41iFKG10DuWwKbsK5YUoSI4LxoCq7EHfr1TDqGVIBN7j5\r\nfpqCJ9sZwN256ip2thjrcajfygSrHWs0kJrNNZnIfFuvK2GXSZRC3S7/Xemu\r\nStPDx5+W6lPBwNznibTIcGYxZOwmeryz/qF3MJJylC8dj+ZVXBGGHVy7eBih\r\n7X94/X0nxGKjZ1BAGT9et1M5xbQ16Z0HKHmsIt3r7b0hqGiIpbQRBinpVV79\r\ntnQ0doTQHLQ+JcJS9ogQ9e48M56YlfDj4ZUp9ryvex2oQojaXQQ0FC6jtAmL\r\noiWoO5HoSFxV6M/mc52Cj4nuFkVM20r9vm3qkY3map66U8j6is1ZMdQZiCuy\r\nV+fBug0nLfwzqir64W4Z5Q1LilD2DCbtvTRSEvqjiLPm9zri4ZJA07XdigfK\r\nUzMcnZ3okP0Y8Mvc13C30RjqiadZYoaHd92ll9JpSngossJooaA86M7Jh7Jn\r\nrOnoNVHPSPmtFXFUwkBk2FALlLIYVHKHyx8=\r\n=toLt\r\n-----END PGP SIGNATURE-----\r\n"}, "gitHead": "8876722fa63cf816a91ba1b34208bff38ff87b56", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_npmVersion": "9.3.1", "directories": {}, "_nodeVersion": "18.14.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/path-scurry_0.0.0-0_1675746340188_0.30346602193175065", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "path-scurry", "version": "1.0.0", "author": {"url": "https://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BlueOak-1.0.0", "_id": "path-scurry@1.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/path-walker#readme", "bugs": {"url": "https://github.com/isaacs/path-walker/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "300f979684469a7163c2fb26944eff2d1c9dd51d", "tarball": "https://registry.npmjs.org/path-scurry/-/path-scurry-1.0.0.tgz", "fileCount": 11, "integrity": "sha512-/nkPrB1RBO4v2Ck5Yo0+r+s0HX3IDrPfYwrnOeBnSv9yryKWwLeFGKNMqYUVieSrMgyVNcQw4PMaDjYmsBD5RA==", "signatures": [{"sig": "MEUCIGfYWGIPhrQU7tOC4FmhZgvfosp77QvsjYnyPIPz5OxpAiEA5emYrvxNc4LPH3Ku3tpN5gdl0P50aKL3plOi+bvc4jo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 245418, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4d6MACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoRhQ/9Ekh2WnznQ7gLGLxG8Vxvk2W53rRSoNwXuoV06+t4WViLVWu3\r\nXaGMQuvorblOj9TAVq2BbLEpHIFfxE/KqTyDopUVZhfIi4VBxpHI8RVtqOXk\r\nHUWncecvNkC3Grv2BddWydyiwPsC7wESCYYbBqOLJwBdVrKxZkyFWqRmVWT9\r\neAY3OSeyrNFJV1ONPhzBl1U0XovA0jTlLv93SEBBdUJkDBtQzG7dnL9h3RCM\r\nF+oojS60QuUw8rrtQxr2df0SGFE7n6e0naQS59dZCxdDa/ps42KowCYi+nLf\r\nr26BC3q6VcUVFiInwm8YVG0mS52Ck5PZXgXjzI79WiZ6SvG2rSid/h1ZT5LY\r\nArFF+rC2e319CtOumEtXGef8NynG5jrccsHVRYrlrpUGZk/B3tLSG2q8iXVe\r\nBOorIHiKarPOkQzqObxhdBQfvqkmejU1zZf/NlvZcyzKyn2VJMTjrzjBlRkA\r\n1T3FKSDLAKQVGJ5kDQxNYaz2PskgEyDtZpX9OQqtEGIUH5FZiZLEhi7VaFJ8\r\n5CLtgWlqeRXIrbDcNW6oOVBvlFylV7igRUW9o/emqupb8jdmDHRtlNUH/OHw\r\nbC+LVKxDWhvcBGTDiQT5Rl1vwoP9L/bYLPBIosJBz8+/SoR0tx5ssm6MknyN\r\nG/Dqv7s7+8+NIm7PTaEC3UTVM2+SG8wPHkY=\r\n=sz2y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "541e5ece8c3c4b708c4468aa49d5429ead64c717", "scripts": {"snap": "c8 tap", "test": "c8 tap", "bench": "bash ./scripts/bench.sh", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash ./scripts/fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 75, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git+https://github.com/isaacs/path-walker.git", "type": "git"}, "_npmVersion": "9.3.1", "description": "walk paths fast and efficiently", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"minipass": "^4.0.2", "lru-cache": "^7.14.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.4", "mkdirp": "^2.1.3", "rimraf": "^4.1.2", "ts-node": "^10.9.1", "typedoc": "^0.23.24", "prettier": "^2.8.3", "@types/tap": "^15.0.7", "typescript": "^4.9.4", "@types/node": "^18.11.18", "@nodelib/fs.walk": "^1.2.8", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/path-scurry_1.0.0_1675746956480_0.36935236169837005", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "path-scurry", "version": "1.0.1", "author": {"url": "https://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BlueOak-1.0.0", "_id": "path-scurry@1.0.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/path-walker#readme", "bugs": {"url": "https://github.com/isaacs/path-walker/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "278c4832c246820f3a504d7911829bc3e8983483", "tarball": "https://registry.npmjs.org/path-scurry/-/path-scurry-1.0.1.tgz", "fileCount": 11, "integrity": "sha512-3Xmmh2RKoPyqFbxxOsw2VxOWf3tzqCq3u3JToHzixtPWKDR5W4AIDKZp8rZTGs3AiG7bmkl67k8Yapz8PoFg8Q==", "signatures": [{"sig": "MEYCIQDVUIktCDUmDW2hsHm1ssnI3v1vzF29lk6lFRnhO/+ZzAIhAJduyOXrEOYstjp3Wz0HQOih56fZWb5LaeLZuwaLyk1H", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 245418, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4d7rACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpk6w//SaZVYWtnf/JmzV6Jb4/5Ym7LsaKmKUfDFpEg1GuqOZeI+Ngc\r\njzjnr5qUa2i60y/ckl7+4iPeeMINXhTlOniMYzE1c8l3W3mXqXqDs9mXUPiD\r\n3N3Wo6nfahtnuDl8giSwecrTb1UUhjhShmbv8G+rMStLKe0QRsHrQgTfA3DM\r\n4pefo/CX/bOCYTwXvZlsciX7dlAzu9q2Hjfy1InvDO70kU94hlYflUx/8n5L\r\nPfaiXvfJwsWfRLSxTShDbg9Q2i4cbK8KOeAeINC06xiQWpAdR93aNoSDapS+\r\nnnoR2Ln0tHPQowxNBnWL2aQ8SWVBOXvFONAUx1Y0hALCtH5MKXPppfrItkWI\r\nF4YTVtqJ0ORgmTLamcBo6d8igrB07W3JqeybHS4pqXZ6oCdJPALKHJBNPfLV\r\nINA73guPqOBffNMSC4coYoHF3gkDM4RSE+TVVyOlcan4n8XLLS4VzpB32V8Y\r\nmIX4jVjsLZ08tEfyJAjez8YBLJLEUksllpnKBK3yKdF9cBLIT4R9sfDTU6ms\r\nyF5ZnSDomzxx2o0NmBDT4PkNEppHNMHr6N7xmPqJnJmwQN9r1hdZelhnyD/+\r\ntUxvvDZX9cHHIO7KblvwN1bs5SGG3Ou2aEy6TMz79e9gSgHJHR5BdQIizNPT\r\n9pV3CR4r7B1OxkI9yM9muYbo6a7FjGPk0cs=\r\n=0vI/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "61b98a6f7383e6d06c8128c9321dbbf108b686ea", "scripts": {"snap": "c8 tap", "test": "c8 tap", "bench": "bash ./scripts/bench.sh", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash ./scripts/fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 75, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git+https://github.com/isaacs/path-walker.git", "type": "git"}, "_npmVersion": "9.3.1", "description": "walk paths fast and efficiently", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"minipass": "^4.0.2", "lru-cache": "^7.14.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.4", "mkdirp": "^2.1.3", "rimraf": "^4.1.2", "ts-node": "^10.9.1", "typedoc": "^0.23.24", "prettier": "^2.8.3", "@types/tap": "^15.0.7", "typescript": "^4.9.4", "@types/node": "^18.11.18", "@nodelib/fs.walk": "^1.2.8", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/path-scurry_1.0.1_1675747051130_0.794254507810048", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "path-scurry", "version": "1.1.0", "author": {"url": "https://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BlueOak-1.0.0", "_id": "path-scurry@1.1.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/path-walker#readme", "bugs": {"url": "https://github.com/isaacs/path-walker/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "ff4bbbe8cf747063b3acf38c4f7f7f059c74b504", "tarball": "https://registry.npmjs.org/path-scurry/-/path-scurry-1.1.0.tgz", "fileCount": 11, "integrity": "sha512-a5UTN3D8KsQnVdGGYOwP22dHHqu9Ci3pd/xQwc+dcMqKl1CGu6eINkA+LXD4W/DXGLrKu6tCl9bt4dmBSueW2Q==", "signatures": [{"sig": "MEUCIQCkHWaYmP6jofzyV+Ja4/442P3TYOoKqOfkZs00gAKgQgIgcoLTGIYa76bTtB9R9jIPN4EUlHjwiX9T9DB/4lz5+2A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 260925, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4sboACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp9sw//VF9fBzFfJq/qJ2ZTJ8mD+P5vxa4+bYl81yEhSuwlqlDcsnEj\r\nCzjpfNib4s/1WYixsdRUFULVimMBVNWrKvqF9giUEVGdb4bzjnrQPhMIkjuu\r\nl4G9Va2AvyAtro/qRqNI485HCFrqM2KqAbcDtO9YF0yGHbz2L2C75ot9wEJD\r\nQTZuZE8U1t6gr0dFp9E+CrewNM3GwfuK1Ea/aYoB2SzvUgmYKWftKsiUUxPy\r\nlQl5fok8Ap36WRq6V8gNLlVgWMux43ho0Jw+PQi+SpljNrKhUn4+J8BbZ4ID\r\nnf1QplX28OKUbHvsok0LEw4kzY5mGdcLyXDTvN9rLiGdlbsoXo5gVpNs9CMm\r\neaws7dPTSBC3+nAX1EZbGXxaa1aK9aNvgYjyeCH6suBCavEjpCCo3ue0mHtQ\r\nRXnGf2GRBxfe4TbCCjt0jZzmeJC4ZDZg1atpWmHQNKrssCUhJMEohoKEF31n\r\nq8TrBd12eFrtTQmX/FONXl7eWxQQrn48F5v0DZMm8JUriNpiDqx2Gwo+AcAW\r\nyD3+puAN+aRWYbUWu5XKbOJkRmwc/MmSV2bVdex9AnVGt8UHIAFA34xLPUV3\r\nlCMvx79YA2zNs0VpyOeJzx1ZzuWqQDZAfLQ7yXDWWcutsXEoWzzoyDXGvTUv\r\nynHs7+dbKHM9h55tBfzlQjOvk/Y+u9esD1M=\r\n=nI3M\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "56c627b9e21ada44597fbec4e66eaa3c051c832c", "scripts": {"snap": "c8 tap", "test": "c8 tap", "bench": "bash ./scripts/bench.sh", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash ./scripts/fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 75, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git+https://github.com/isaacs/path-walker.git", "type": "git"}, "_npmVersion": "9.3.1", "description": "walk paths fast and efficiently", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"minipass": "^4.0.2", "lru-cache": "^7.14.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.4", "mkdirp": "^2.1.3", "rimraf": "^4.1.2", "ts-node": "^10.9.1", "typedoc": "^0.23.24", "prettier": "^2.8.3", "@types/tap": "^15.0.7", "typescript": "^4.9.4", "@types/node": "^18.11.18", "@nodelib/fs.walk": "^1.2.8", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/path-scurry_1.1.0_1675806439740_0.7861093374660328", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "path-scurry", "version": "1.1.1", "author": {"url": "https://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BlueOak-1.0.0", "_id": "path-scurry@1.1.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/path-walker#readme", "bugs": {"url": "https://github.com/isaacs/path-walker/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "6ab33e94a85a7b63e40c69146957ca59d5da76f5", "tarball": "https://registry.npmjs.org/path-scurry/-/path-scurry-1.1.1.tgz", "fileCount": 11, "integrity": "sha512-L7T6CZSXD+BCv+ZddphAj4B4UFkIO3CHn9XHg0vACB7Vpsund9PRGsj4lcUnfHR8zuxWEIJekfR1DhFUU/Q9qg==", "signatures": [{"sig": "MEYCIQDW7lh5ctUpWS1KyJV/M5fSTtr2qjCuReFvExE3fIJSwAIhAJpjh29uF30CvxAUqMEavjlmDjutMv0h0L0cbK+1ZFwf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 260897, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5APhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmquSw//QDMEOUlh+9xF5TNlXFIA4bKUpAdhPU8vIb8v9Xla4CjbiF6B\r\nWRBC+g3Ug2OUOrbrUo6SL89huuMZgG6h+GvJ59umEKOKNmO0d5qziSQRvKLM\r\n/YGtYS9k0zwjjvbjW7av47meqnwxOQ5C3d4Kr57DrVu75jRxoXSRiktk6dJ1\r\nFAppe0azz8zkKm27K1hZNmVfkCSvcodbt7kZjl/7DOuZlhmwECv10hZbE0v2\r\nOybQXa4ACFrL/u8wnzzbIhdDf7FKXS9PzQQUgkdkgmBYXp5ouU0aLZ3g/0jh\r\nYva2s9Vp3yr1ImYxUFTRkQxmlZtV7T4SQ+KroAJTLx/iB1R0Zrj6OGls/fVA\r\nLgEHilh2Z28UaxtFLkvVJRKlQbzdjGmAhkaFw35hEkyLAk8aZTwH6VShKY6m\r\nrDHsMshicgxYo4S0otAfS8x7BU1OqluVpc8XifZY/ggWUgrTbVoy1fz/58ze\r\nUjGlch4S3wjEkOI2ITbZxSGLCcXjnqJmgsVBpaDkwFVr6RvWD+PGne8RhDxd\r\nKWMY25EP+JRAxB9ChyE27AEd9F5NNNJhLvTZ9D0K9F8Dpp+ORlhUHaUapuWU\r\nlJL14yAZsQ1oZ3N6SL5R3pEB1f+8eoQG+CACXwhRJvklnaA7Du1VAyJVpXo4\r\nmx6GqDfKFR+phJ5+tTJBmmbtsJFNeTM9IK8=\r\n=+472\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "13c2622daa0b3f40846adc45863dda4ea7fd1c53", "scripts": {"snap": "c8 tap", "test": "c8 tap", "bench": "bash ./scripts/bench.sh", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash ./scripts/fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 75, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git+https://github.com/isaacs/path-walker.git", "type": "git"}, "_npmVersion": "9.3.1", "description": "walk paths fast and efficiently", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"minipass": "^4.0.2", "lru-cache": "^7.14.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.4", "mkdirp": "^2.1.3", "rimraf": "^4.1.2", "ts-node": "^10.9.1", "typedoc": "^0.23.24", "prettier": "^2.8.3", "@types/tap": "^15.0.7", "typescript": "^4.9.4", "@types/node": "^18.11.18", "@nodelib/fs.walk": "^1.2.8", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/path-scurry_1.1.1_1675887585734_0.27038129107316244", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "path-scurry", "version": "1.2.0", "author": {"url": "https://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BlueOak-1.0.0", "_id": "path-scurry@1.2.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/path-walker#readme", "bugs": {"url": "https://github.com/isaacs/path-walker/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "1f838c0b50a98b05eb082faa1617b47535acd601", "tarball": "https://registry.npmjs.org/path-scurry/-/path-scurry-1.2.0.tgz", "fileCount": 11, "integrity": "sha512-+4ziUPFIhQA8uuTcq2cKF59ySJ85v4t57+DEEY2QayNf9UfHKAuLkr2OSOySfPIAZOtag1w5Htw7glKT71KAPA==", "signatures": [{"sig": "MEUCID4qa3dS07QBov22gPNeG/4v3C8pqLJoz+jDb+r4jR36AiEAiK8v30TU4Flp56K4RM3jQ5pRJ1azlwI58iSol2zTa8k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 267344, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5ULtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrGFQ//aS6w/4n7q3dhTGcQ6x9FqueGMxf9VNpp1n+jC4Vt47nH0G9/\r\nr3HJGRs75jaCDxoiCD8W3imeK1aNJ0y5qsZa2DqGrDl5iLxgylbFx5inzEYc\r\nHNh+GTQ5KErqxqZoMRKWMy81pUIaY+elc9acOMCVU0KnMkdLD3SS44nJKThy\r\nipY5sNFn3K9oVSixwqaX+VSQ+0ep0XvODxnXvujQmm7Ib9GN1ibVinfH3Xa9\r\nXPKuBxcedTkNSA2I1e+TrNtEK2PyuMge7ZX0WIICe4v4ypS0CzY8XYDF24Li\r\nJqNWMJEvPECAWSGDfUVm+47DH1OUik/qwRVChFA/dHFLARwqvDmL1CvsFP8j\r\nv+2kDsEl+7x6DGCPRPfBhoqPzSXyhfBT1MD5xSzYhWeWCHgrUbOOhJ22STiu\r\nCacLxZ0OTQrABZtyDtmJvRIz8Zq/D+/i4X41MikKXfI5MEBgwbq4V2J7tTkO\r\nHdZTLDXF++sg7fNmYm5BOUOVdOcG0rarD8fnLuDlHOJZ+0tEfHtR2flM7/EC\r\nsCylRBGJs+QfPist3W9sBcG1p/r4jma+taDy8fSweKPUqIBUkTDSJStus7jq\r\n63dx8qxt6Iaetms2DhqlYVoCs34akgFquLGRGiW6laoQFLJDwxu+GZZFfbv7\r\nwJDohfGcnAMYk5nNhr8cfR4xhH9uNggGQKA=\r\n=Z/+t\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "55b385f66df43a6fa82e9de2b8131846c73a5e93", "scripts": {"snap": "c8 tap", "test": "c8 tap", "bench": "bash ./scripts/bench.sh", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash ./scripts/fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 75, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git+https://github.com/isaacs/path-walker.git", "type": "git"}, "_npmVersion": "9.3.1", "description": "walk paths fast and efficiently", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"minipass": "^4.0.2", "lru-cache": "^7.14.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.4", "mkdirp": "^2.1.3", "rimraf": "^4.1.2", "ts-node": "^10.9.1", "typedoc": "^0.23.24", "prettier": "^2.8.3", "@types/tap": "^15.0.7", "typescript": "^4.9.4", "@types/node": "^18.11.18", "@nodelib/fs.walk": "^1.2.8", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/path-scurry_1.2.0_1675969261567_0.9956218329003597", "host": "s3://npm-registry-packages"}}, "1.3.0": {"name": "path-scurry", "version": "1.3.0", "author": {"url": "https://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BlueOak-1.0.0", "_id": "path-scurry@1.3.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/path-walker#readme", "bugs": {"url": "https://github.com/isaacs/path-walker/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "10c3cb61b4a8180030107dbc43dad3042511f9b7", "tarball": "https://registry.npmjs.org/path-scurry/-/path-scurry-1.3.0.tgz", "fileCount": 11, "integrity": "sha512-S1tMxbHwgGIPyf9e3caxqQKyxooMYMLPBI7hAvGHHaseqKwXgJISYBe9zpj3aUsAOGobb6tR24c3ebhmidb++w==", "signatures": [{"sig": "MEYCIQC3yz/aGC/ZhKPRI1CI/0cuLUEPSiA3ywnOfKNJwGIdNgIhANEjXsgf6WIY3FGVC2sMZyepYQ4pkUNPuhld/esvJt3E", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 270721, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6HBsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqjSg/+OSD04/4/W8KqXcKzbPLoTHBwIYXC/pXylZkAcF4RjFv59aAx\r\nQsGZm+HXxgjR4gLP2/paNK56Gb7++AWIYUY7/CYlwfhn/h7QwEiwuLW7pX8i\r\nbIqvqNWFHNABWwFp1G1YJwCczIH27eQRlGphKq9jFqF0kzGMTG7RB3MwER8q\r\nJBzq8mo0Oqf/lZHCSOs/C001Fn7PRHtpOhe31H5hdQRNk84zJW/c/TRXtBMh\r\n6dfKtqD1Co4FQu4g9QyBC98Lj9C7P1iNKfFTSvrxD2UDAof/ld8aYq58sSbe\r\na03kiFcn676UVNEQAEpBMxmcZNo/3Jzdleu6m04pAvxetqbJeJoZtWldILBN\r\n9NKUTz7mRsm5HPMjjOvZxvxzXcxATz6nKtGUNYkh5YAyiwEm/DM5bBQTsFED\r\nkt5EDsWep94ackgTmr4SPZJdFVvialwySrjOhDbci/sqh52RzDGgTcVmZxfl\r\njokpdHB777Ln02OxhFYbnvARfXH9B8Ba9d6VTG4E8TT6TqYoRE7a1JOBbUFb\r\n3QVtidtlTkyoH+17nGqX+ebVsbR4LGXGmI8F4acWmO5JR5ojbYzgA4/z604d\r\nPZFfFBFAWURP7WOwnq4lAEnjaBnvTo3m9381JRcEQgxz5sPUleIr0h23zFzr\r\ngTvXB9TvVDlzmUibNV1FyZM/s+t73Gg5pok=\r\n=0kO3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "1801f55878a5cdeddf1e3b40b4c272f6dae71282", "scripts": {"snap": "c8 tap", "test": "c8 tap", "bench": "bash ./scripts/bench.sh", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash ./scripts/fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 75, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git+https://github.com/isaacs/path-walker.git", "type": "git"}, "_npmVersion": "9.4.2", "description": "walk paths fast and efficiently", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"minipass": "^4.0.2", "lru-cache": "^7.14.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.4", "mkdirp": "^2.1.3", "rimraf": "^4.1.2", "ts-node": "^10.9.1", "typedoc": "^0.23.24", "prettier": "^2.8.3", "@types/tap": "^15.0.7", "typescript": "^4.9.4", "@types/node": "^18.11.18", "@nodelib/fs.walk": "^1.2.8", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/path-scurry_1.3.0_1676177516051_0.0752959602798593", "host": "s3://npm-registry-packages"}}, "1.4.0": {"name": "path-scurry", "version": "1.4.0", "author": {"url": "https://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BlueOak-1.0.0", "_id": "path-scurry@1.4.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/path-walker#readme", "bugs": {"url": "https://github.com/isaacs/path-walker/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "adb6fd4d7bed3e05738647de4d56ff4245e4d790", "tarball": "https://registry.npmjs.org/path-scurry/-/path-scurry-1.4.0.tgz", "fileCount": 11, "integrity": "sha512-kSNY3gm5ul3nBwDFkX9i8pkqZ5r0YsutWwpaUmog0utwOGwiRBiJlks955VGSsKde5EmviX02DlEDEWj7miukA==", "signatures": [{"sig": "MEUCIGfE+EKH1V5Lw+PHeOO/r6bGxunzF/d/Qy3MZyR5pBJSAiEAs4DvhRYhooVimrtCXMBocf850r/Cl58sMupJ+4TVtfY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 278090, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6mqUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqV7hAAmIp626jRda9vCdzOt7maMcz+nrizsof/MaQp8LYUKu+dG3zA\r\nPohqVj3tFATuQVV6DHtxSv1TfxwTvk0GmohW4Y2XB2HEbxMSzLnYx64ot2rE\r\nwLhKPSyiA0IAVjne/f2DD3+zNy2VxOPtFBJPGMgofKQ+mKxuKnuBfihbGE8s\r\napv7HQZgH0ARzT4DnPpjyKsI0/I3pxdLZyTyMksNrz7buyxJtR9lSnBRXbxf\r\nL28HnCex/+RCiyCUDCIUPoeDrWJ+lY0rryoRhgBwlsBq19dVYHcWSlnNKcRU\r\nqtN9rfNmXs4zPY52b0ONzH8HvhJ8HnjrPfbTiXTzkD2apVcPBGWxW7Nh+kuX\r\nYeTQ03uU+XW80LYWzfnuOf0BQGphc1AsDTYSu3DigNrO92QnTUORDDDrb6f8\r\nLhUf0gVfIIAZcmPKiPwUP7qbvVg0iEej1tgWWf+6ydVZpyFbMwMHw5Td5/qv\r\nuoPktzFMyuCMb1hjhOnaAUkv/zOYU9ljLV+MXyjtUs9saUXU+ZBUC1uZphZP\r\n+dNPTbo/NOLqO3JdFjvJo/krkiS5Lvn31USShXJpUx/Q3GwBGIVlMJnrLob4\r\nwUYE2nUlzDzbsB7Ww0RcB9GkUbKOfcpENsBNw5vHd/YF/D0eDWKAHbde7Vvk\r\ns8bBTREcgyDFuZhPPnKtvbMZd06BgJVwbNg=\r\n=c6HT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "cdfb59ae683178d76590e6ad400f80a9de003c0d", "scripts": {"snap": "c8 tap", "test": "c8 tap", "bench": "bash ./scripts/bench.sh", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash ./scripts/fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 75, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git+https://github.com/isaacs/path-walker.git", "type": "git"}, "_npmVersion": "9.4.2", "description": "walk paths fast and efficiently", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"minipass": "^4.0.2", "lru-cache": "^7.14.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.4", "mkdirp": "^2.1.3", "rimraf": "^4.1.2", "ts-node": "^10.9.1", "typedoc": "^0.23.24", "prettier": "^2.8.3", "@types/tap": "^15.0.7", "typescript": "^4.9.4", "@types/node": "^18.11.18", "@nodelib/fs.walk": "^1.2.8", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/path-scurry_1.4.0_1676307092163_0.24390464046677263", "host": "s3://npm-registry-packages"}}, "1.5.0": {"name": "path-scurry", "version": "1.5.0", "author": {"url": "https://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BlueOak-1.0.0", "_id": "path-scurry@1.5.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/path-walker#readme", "bugs": {"url": "https://github.com/isaacs/path-walker/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "ffc479e688064d16fc80fa8890389c56182c6463", "tarball": "https://registry.npmjs.org/path-scurry/-/path-scurry-1.5.0.tgz", "fileCount": 11, "integrity": "sha512-hJ8rODLI9B2qwsYAd32rrI76gwVUPeu5kq/do6URDj2bJCVH3ilyT978Mv/NLuFMaqzHrn3XtiDLMZHaTTh4vA==", "signatures": [{"sig": "MEQCIEh+C5ZMPaoqRweJRXxd0i5+QH16h/EA1i0TBj1CCrfsAiA31kc1t56RiGflVDNP0dymGWGOCUvMXfUcjpNdn8juVg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 282190, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj/ETwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp1Fw//WairhiKJrNJtNVs7v8bthTOTRgpvD8C1Haz7W4byyllbGtgL\r\nhvpZJreUzJxOkuEaFt+jAap7Hc6ghU2IA36KRconjH593+t6yuLpjaS+UfdZ\r\nAz06ritlecf5ZKNXnj3m3QlJRM4QKbfZS7TeNxRWgQnC+EHdIVXmNoTjsM+Q\r\nacQQuO9JmKSjoKcgKVq8BBkQE2xhKwZhrwrZotWFIvGuF4q9T77hZ/aa7DvH\r\nlGrPRUpEFSueWzut9QAbNj0+SqR1JujMFWB5hkVq0To2Npu5pczRUYL//JcO\r\nlZuc/6tjtH3qW4eZhuVbBG2+VnLAcuviBAx2LgCksqdK3Zi3PQQAK84wnGcs\r\nibgglOXRBtg4NM4B+GMhEQ1bVcZn1PDaLyQdZtJx7DY8n9d99rNWnrXn8UjP\r\nu2jSx0mmfjiMkjCS54h+fCr7Aduz+xxsHd6/31BjLYSKOL/FLpFvo/25nlW4\r\nBizhXVLDS1Qhi8/rakdLyc6xbXP8wkdyRcKPeHwcSLDpMctrCM3toP9s7KL9\r\nxxI20/hTRR6vz+GHxGGz2CwMcrKmca165uuMxz2PgiXUYmzHd38AohsUWJsu\r\nbsyQTT5VtiVYLZVQ5erklgEBLiPugbDmSPj/8MxDfQm9OuNpmYy3MSOPvtle\r\nwjrGDkBBZ6eUZkSRPyZPaMe03/RPwEBTiR4=\r\n=07Fz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "e728bb69816ce7170e5fd352fc36872444fdcf1f", "scripts": {"snap": "c8 tap", "test": "c8 tap", "bench": "bash ./scripts/bench.sh", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash ./scripts/fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 75, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git+https://github.com/isaacs/path-walker.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "walk paths fast and efficiently", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"minipass": "^4.0.2", "lru-cache": "^7.14.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.4", "mkdirp": "^2.1.3", "rimraf": "^4.1.2", "ts-node": "^10.9.1", "typedoc": "^0.23.24", "prettier": "^2.8.3", "@types/tap": "^15.0.7", "typescript": "^4.9.4", "@types/node": "^18.11.18", "@nodelib/fs.walk": "^1.2.8", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/path-scurry_1.5.0_1677477104534_0.8688808264117334", "host": "s3://npm-registry-packages"}}, "1.6.0": {"name": "path-scurry", "version": "1.6.0", "author": {"url": "https://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BlueOak-1.0.0", "_id": "path-scurry@1.6.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/path-walker#readme", "bugs": {"url": "https://github.com/isaacs/path-walker/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "5b95ecf5520b939f920f347449b7a8a3d844bfff", "tarball": "https://registry.npmjs.org/path-scurry/-/path-scurry-1.6.0.tgz", "fileCount": 13, "integrity": "sha512-WMhQ33uXDbazvo9oFEUeh3m7C9UiyoHbsaFTyocVbuu9YfEyVx9R5IU2wZuhks0JlkIJ9DuC/60745oYfQHRRw==", "signatures": [{"sig": "MEUCIQCWordBGxIXHDbRGnsNbo3dg6Viu08OlnIfywR/bIy1QgIgdUQghBZFJZf3eUIq6SjFgYehL6kzZRI4YG84D8K4yoA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 338906, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj/8oEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoMxg//Synd+TRMqBO3Z5r9DwY2E7bgRwzp3VQdOu3z9H28473ej28d\r\ntnnLYYjnkppDSearKL58ekjbQy/BUMkqI2ur+3myw/diiAnif4DMzIg5JJ6m\r\n+Fch74HMCAENCCViqmt8BJegWxTlJm3XyhzMezvLS6kiqhxcsIOIqFeS0Ky6\r\nceIZX2Ela0iCQP9zdUS7fcpmdeMVEPnC8Meq5dhBy9UMihH9X4/t/pctiXRG\r\nzkUJT3k9gDhf8/hxhd1AuizAFogklsYWZvRaj1hysdWU90sGe6f2tIeMXdea\r\nQF0nElk78rbh8iGZr/UkIrVWrxxCEm/PJKiqgeT/KlEfMKmiXKI5/GYNg7Ab\r\nCD/Byf6fDsrbEM3A9ibU0f1DkxSi5yNjarbCbomxYablABGqvlQcZOCp/3f5\r\nMnmGFHAyK/iDFYu4VZiCWCqCgRFkgmmPpw3YKnzlVhlETMsKg2gCWobP+YIZ\r\nUahLBxbV5LoG1IjIy5pcI2C7SNVbs7mYLpltVsQWk9azaH8WMuetxZZYn1K7\r\nJLRP2Y5nzoKIImMfqFi0weUiWWfDu2TI4k1JuvYr/ryexbivXanyHBqcnSiG\r\n1M2L0Nnb2HqfVUlqd1x/FCUW4ITU8DtzTDP4kR54+0oV4NRtz/nWTcIxd3Ix\r\nrunR46uDy31OWCGB2Rur5S5EqVxlOM5GcdI=\r\n=qH1P\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "af19b853eb590d5618db422ed39017224a22f61f", "scripts": {"snap": "c8 tap", "test": "c8 tap", "bench": "bash ./scripts/bench.sh", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash ./scripts/fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 75, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git+https://github.com/isaacs/path-walker.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "walk paths fast and efficiently", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"minipass": "^4.0.2", "lru-cache": "^7.14.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.4", "mkdirp": "^2.1.3", "rimraf": "^4.1.2", "ts-node": "^10.9.1", "typedoc": "^0.23.24", "prettier": "^2.8.3", "@types/tap": "^15.0.7", "typescript": "^4.9.4", "@types/node": "^18.11.18", "@nodelib/fs.walk": "^1.2.8", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/path-scurry_1.6.0_1677707779962_0.27064321298974625", "host": "s3://npm-registry-packages"}}, "1.6.1": {"name": "path-scurry", "version": "1.6.1", "author": {"url": "https://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BlueOak-1.0.0", "_id": "path-scurry@1.6.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/path-walker#readme", "bugs": {"url": "https://github.com/isaacs/path-walker/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "dab45f7bb1d3f45a0e271ab258999f4ab7e23132", "tarball": "https://registry.npmjs.org/path-scurry/-/path-scurry-1.6.1.tgz", "fileCount": 13, "integrity": "sha512-OW+5s+7cw6253Q4E+8qQ/u1fVvcJQCJo/VFD8pje+dbJCF1n5ZRMV2AEHbGp+5Q7jxQIYJxkHopnj6nzdGeZLA==", "signatures": [{"sig": "MEUCIQCER2LrjgnZCJWbSXjo7hySWxtMk8ga7FVXI3xDfcSy6AIgWiVY5DAy/eqizQq19k9vfCzsqou33HKpOzLP6LXMwrU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 339176, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj/83kACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrAdxAAgvlylMPVCMjkZGvQPtqyuCDVG7DiSgAH/2hdYqL85ZgXjdrl\r\nOuo60QfgrDRMS6Vm6kVzAnz1RT74csLR1YnwtKUVA3A2iO2xt9x85Gs5JUkg\r\nKM2UGvevLK/Mio0U/0PxKiJYW5D3UvtALJE3rg4l01Y2ZTXwu49ZJwtsrum9\r\nwB5qKgqmTn2Glo04ZQY47uoimCUJSf7f+2p4L1MZDe7Y9FMMzbg6BvJip9fE\r\nrF8N2u82bulhPO0vhWfXSsM8JDrf/1jO/kv0A9rxOn7J7sgAoWTaPbRzBE2r\r\n6edOUiZvllR2mgZiz0RbXMFwuZVAM6OR1uI3fFmyf21uxQuzgQ4G81cTs1hB\r\nujXtiaFrDNAiDxukrUnmJrFrno/4XUHCB1iHMqCZInjaBnPljNgbeyavehNl\r\n17hPYWqErzG/hk2KWbMxUneShtvQx2wewfyimtePxCDch6L2nzmh+Uax1k3G\r\n8RofrAygn+vYfQ/VN9PjrF6wStXt9cGSK+XiT0tCmo1KsRf8pMvUwFlbn3zh\r\n8jkdqJjhspvHcriRqZldczprJ7pnxMTiVN5M2NGORaF5hIAjGZIEU9gDSaNO\r\nUknrbWoscSAMP7fcnxrFVPxIY19BPjOviTum2lWWCtWsAsVEcDN1OTJmyLii\r\nvUIPidt+hIrW8VNLv7usmEUCFNurgBRXQ6o=\r\n=iWZx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "ea217574f07fd6625f19fef7f3ff9aa6aaf16b92", "scripts": {"snap": "c8 tap", "test": "c8 tap", "bench": "bash ./scripts/bench.sh", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash ./scripts/fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 75, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git+https://github.com/isaacs/path-walker.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "walk paths fast and efficiently", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"minipass": "^4.0.2", "lru-cache": "^7.14.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.4", "mkdirp": "^2.1.3", "rimraf": "^4.1.2", "ts-node": "^10.9.1", "typedoc": "^0.23.24", "prettier": "^2.8.3", "@types/tap": "^15.0.7", "typescript": "^4.9.4", "@types/node": "^18.11.18", "@nodelib/fs.walk": "^1.2.8", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/path-scurry_1.6.1_1677708772009_0.4774292827302069", "host": "s3://npm-registry-packages"}}, "1.6.2": {"name": "path-scurry", "version": "1.6.2", "author": {"url": "https://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BlueOak-1.0.0", "_id": "path-scurry@1.6.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/path-walker#readme", "bugs": {"url": "https://github.com/isaacs/path-walker/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "72783838113a4a0cec155323b2c01eb1b193396f", "tarball": "https://registry.npmjs.org/path-scurry/-/path-scurry-1.6.2.tgz", "fileCount": 13, "integrity": "sha512-J6MQNh56h6eHFY3vsQ+Lq+zKPwn71POieutmVt2leU8W+zz8HVIdJyn3I3Zs6IKbIQtuKXirVjTBFNBcbFO44Q==", "signatures": [{"sig": "MEQCIGLGqwxhTVitgDhFqW8eF/yk9c5ZrhkgN7zm9E6MMq/TAiBqLVDFQJQG2jD7RZAKw9x7L8kNQZU8JpShrQrdI7AOIw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 339190, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkGpKSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqymw/9HK+AWK0CKKpmmdq042ELJlvwIS+yt+Ebbq4wsRekhdXerG3j\r\ngRHOkVR1IXn9CubAD5mrVqhVv8OVCz4ozQQZAslEwYkCq8vbOuj2lVk+ldqk\r\ni/gzPfvvAOEkVJZ79DZXU7XnLMOOdlNi2kr3srk5Z2SlCVy1rQyrfXyPj4VX\r\nzoCwoJy+P/OgrqwOebrEr8LRD+NB/+9ZhdZWoitx81jXwFyL9XR9ZObWPOt9\r\n4UIFkUP1aXVqsGW8D3HobxZ/i8TBds/x/xxN1RXrjuYwnYh+p97ggqNjFwaD\r\nzoeOk2DACs5mTmxTx2GxGsKjoIHh2vyjkp3mvsIQIM7WXf9y5IoUI/JBd8uk\r\nRgD8/VNxzznQmmlTdC9Xpj9/dYrdWSVGfSNk86vYLwtHWVQA92lfcwtYJ9pD\r\n1r1EnWAXPUm+/KCEv/qd6V1mVT62Ee1fxpznIY+bch2YcP2PUFZ9e44Tvgba\r\nfLqCzaf5RjmDaHfyLEQiFZWWxa0w4fL4cerNH9E3gMZaE5NgwBSYFn93howu\r\nmNszwOYnZQlr5YAJKr/tEPg/gY3ccMGhCV5FAWBzm3+04VzyD7pqgpFyeufZ\r\nUVApWzN21RAtI2b6qFZBBaF0pdX3Da4sooh960nhJUvoK49ASusSYlwITyid\r\nfVRjFHKiNNZfV3gRyvT/SiNZlxFAMY4Q51g=\r\n=DyB/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=16 || 14 >=14.17"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "62963670fd674531187055a70901522fc1a7943d", "scripts": {"snap": "c8 tap", "test": "c8 tap", "bench": "bash ./scripts/bench.sh", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash ./scripts/fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 75, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git+https://github.com/isaacs/path-walker.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "walk paths fast and efficiently", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"minipass": "^4.0.2", "lru-cache": "^7.14.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.4", "mkdirp": "^2.1.3", "rimraf": "^4.1.2", "ts-node": "^10.9.1", "typedoc": "^0.23.24", "prettier": "^2.8.3", "@types/tap": "^15.0.7", "typescript": "^4.9.4", "@types/node": "^18.11.18", "@nodelib/fs.walk": "^1.2.8", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/path-scurry_1.6.2_1679463058153_0.8159383871835495", "host": "s3://npm-registry-packages"}}, "1.6.3": {"name": "path-scurry", "version": "1.6.3", "author": {"url": "https://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BlueOak-1.0.0", "_id": "path-scurry@1.6.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/path-walker#readme", "bugs": {"url": "https://github.com/isaacs/path-walker/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "4eba7183d64ef88b63c7d330bddc3ba279dc6c40", "tarball": "https://registry.npmjs.org/path-scurry/-/path-scurry-1.6.3.tgz", "fileCount": 13, "integrity": "sha512-RAmB+n30SlN+HnNx6EbcpoDy9nwdpcGPnEKrJnu6GZoDWBdIjo1UQMVtW2ybtC7LC2oKLcMq8y5g8WnKLiod9g==", "signatures": [{"sig": "MEUCIQCgHLwi9wS140ByCap2a5w05X8tkAP1k6pZmrMyMCTyCQIgBknDswKSO63xcQMVsruy9+v3C/3+6uT9Kmi7jGZsk5U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 492076, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkG04HACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrguxAAmCX+nrauK8Rfu8jImuQY8ahKCLkTO875Rc7TIcfDWXeaK90Q\r\nFd0guKJ/C2NmhMBEgPLQwEmp/x+k1SIBfxUpKib65g8nWXku9sS+RAv4VVGE\r\ns0dOG5alKOYs9Vcg7lmdogqdxeM8hrKQmMrEIMLHDu93qYillO8Y5oqsYaY6\r\nu2okujg7e6lLAUExEndlpuKNsEPNe7Mk6KD9b1EdQVNtrpN2SZLvVT6yU1Iv\r\n/DHOjbgVqL0nEjVu0zq1qaAT+VG4GNqEjlrjgsATFGy0DeszRIZfPmT82nrw\r\nNychKzrjxztCFT5weAm1ICFOpHMaJvedUQtUdUlXebKxAN8Up33CedqC7JCM\r\n9UfUjYaCI3m19ywMM6l8CfRRgUvLy1vxAFMPw0GKDZjJXAfWyzEXh0CLTE5B\r\nbHwKl85thYb48Eevp8PK4s2Z1HBxe92tcrkLeWaeKHrpufQwKORNEV6i7Ad6\r\nBoIuzSQGGDdbofrNO9oDLDIB2QAG1pSxJ/uPY5czAybUW5gOUUWCtakJSuGE\r\nepgGYwmrynLJ77fgpfvPuzaYFtwH+vdBKYUJxtXCav/ZrNABorXo4h9fSeOA\r\ncleIbFAfsQ8eIz92yBRyylUi5UU5Fv60eccJn7ByqrZpi1+a3/lalVyzbGh9\r\nxkeGAsnYhCQnIPoNMnbEMDeR7i4QePsXS/o=\r\n=LxG4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=16 || 14 >=14.17"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "ea382985e8250a16571d81ea730d9e450ea1e9cf", "scripts": {"snap": "c8 tap", "test": "c8 tap", "bench": "bash ./scripts/bench.sh", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash ./scripts/fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 75, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git+https://github.com/isaacs/path-walker.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "walk paths fast and efficiently", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"minipass": "^4.0.2", "lru-cache": "^7.14.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.4", "mkdirp": "^2.1.3", "rimraf": "^4.1.2", "ts-node": "^10.9.1", "typedoc": "^0.23.24", "prettier": "^2.8.3", "@types/tap": "^15.0.7", "typescript": "^4.9.4", "@types/node": "^18.11.18", "@nodelib/fs.walk": "^1.2.8", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/path-scurry_1.6.3_1679511047440_0.8217690084847757", "host": "s3://npm-registry-packages"}}, "1.6.4": {"name": "path-scurry", "version": "1.6.4", "author": {"url": "https://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BlueOak-1.0.0", "_id": "path-scurry@1.6.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/path-walker#readme", "bugs": {"url": "https://github.com/isaacs/path-walker/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "020a9449e5382a4acb684f9c7e1283bc5695de66", "tarball": "https://registry.npmjs.org/path-scurry/-/path-scurry-1.6.4.tgz", "fileCount": 13, "integrity": "sha512-Qp/9IHkdNiXJ3/Kon++At2nVpnhRiPq/aSvQN+H3U1WZbvNRK0RIQK/o4HMqPoXjpuGJUEWpHSs6Mnjxqh3TQg==", "signatures": [{"sig": "MEQCIBVrj314uXt/ipEsdLFJMloM5YRGecEL0dgf877/tnpmAiAwz5GLPnAkPH92yEt/FHTu+NxJijO8ur4luxeTVdLaog==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 492003, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkMzcBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrcUg/7BxTkXCOgUmolkCeLkjLIRqS2RozqmXlQkq72GiWlSoRbJWyi\r\nqZW/x9ouCG/tyypn1MJzqtSSvzlTiaRzbxQ48LQdPRsoDDVY9SfO9CEwEAiQ\r\nqxoSEBCwhsh7hnb5ywA4rDFNA/EhZMf0HbfyYO2Nxoe/p/RdKx2FWLQTyccO\r\ne/rsEKFbpWFQwRUgr9b4thzBHnTlxhOQL3+Bn51IlNIZZukbyPfGByf0yXmy\r\nKnLcOpKWw9Yy8zROuJgLuYSLlj+56pfsycU/hchhsBaLeaM/gJqUQ7JKgoDp\r\n3XZgHYENw/OKhw1wDoXFQcTKR+e9gmq68Swc1wXhLhUnY95aP4kc4hTiEwmv\r\nVqsMevMNUl71PFTftfIQcjf2h65+TVrqAsDYNcu1fZAuatRC+xB1/qbIC8FE\r\n1DZfZI3EQemimldEIfms6aIfT0UwyTYrxlzLI8ZqZnYMAS1H6qWeaA+N58Ou\r\nhSlZNzsDCEzqTB5kfZS58ZdaRbHXL+Y3Wz9HEX919286y021ea5+jgYH6/9+\r\niT1asgg8fgdnXIAGQCDPQziKUixxu5VANNm25E8pJ7KNjs8WF1V05t9+5Nz1\r\nv5FZB/QHYDyCtUQ2W2KlytOgX6NQed4MwnTX6c0S6r1bJnnhVRfWanWaZ5sl\r\nH6sxidKT8WeQ0Ckm+stcZKc5lATqKror+/s=\r\n=k9oj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=16 || 14 >=14.17"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "3cee97c9842dd2548b741dfaa8605fc0fcc9bd78", "scripts": {"snap": "c8 tap", "test": "c8 tap", "bench": "bash ./scripts/bench.sh", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash ./scripts/fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 75, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git+https://github.com/isaacs/path-walker.git", "type": "git"}, "_npmVersion": "9.6.3", "description": "walk paths fast and efficiently", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"minipass": "^5.0.0", "lru-cache": "^9.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.4", "mkdirp": "^3.0.0", "rimraf": "^4.1.2", "ts-node": "^10.9.1", "typedoc": "^0.23.24", "prettier": "^2.8.3", "@types/tap": "^15.0.7", "typescript": "^5.0.4", "@types/node": "^18.11.18", "@nodelib/fs.walk": "^1.2.8", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/path-scurry_1.6.4_1681078017016_0.9187239590094898", "host": "s3://npm-registry-packages"}}, "1.7.0": {"name": "path-scurry", "version": "1.7.0", "author": {"url": "https://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BlueOak-1.0.0", "_id": "path-scurry@1.7.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/path-walker#readme", "bugs": {"url": "https://github.com/isaacs/path-walker/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "99c741a2cfbce782294a39994d63748b5a24f6db", "tarball": "https://registry.npmjs.org/path-scurry/-/path-scurry-1.7.0.tgz", "fileCount": 13, "integrity": "sha512-UkZUeDjczjYRE495+9thsgcVgsaCPkaw80slmfVFgllxY+IO8ubTsOpFVjDPROBqJdHfVPUFRHPBV/WciOVfWg==", "signatures": [{"sig": "MEQCIHblwOFaogi8O5RTcOXSfL179K0xiGqtGXs98Ze6psLWAiAd7/dKk2VtiPNgqEv+qTKqsMF7DAzQMaLkMmCiEy4ivA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 514567, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkOdsOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo83BAAn4S3CILxPicbWfIV0A72CoXR61hkptl9vhh8xp17H+lNw2Ud\r\nwUdr8Jbhx2SAYbj4oPxA1n29JgCrH584VXpb2IvTw99/U8zH/Vqwa9eLPtJU\r\nf99JfwnnNY930ydIzJHXTLZR+vdGt/ZFNs5ffGoe+WYpkt1Fn8vYAODx55ZV\r\nH2+wMRIhhcjHLOfP0HRQeVZbWqhBt6lJB7w2Mdaj2f3GkR/7xORolfpNE7m6\r\nnsTcQhToAhJbNSKGYDo6VxT54/cQ0OaY+kYusNyzslOQazgPHEye5MRNtErs\r\nXNAm1etmQmDGrozXL7uaNchDGs5a/7K0du4P2YwGBfflQaJYBfnWB29jTD8R\r\nJccib1dyS3Bke+fRzruYMcu3x4dF1xj9zOjDe9Zv0x2muMziBnf4OGumq31v\r\nyAWBHitdmd7ZWqgAo2vQnaG5zMEqKwTeqMvDItMQIDJb9p7qch6sVylGLIhb\r\nRV+NCN4ehKtDClrI4X4Tm2cH+g9b/QuNJ5dHq8D9V9cI2UC2hf/87wQyYkm0\r\nNXgVzLeQ31NKCwpMX+ALxWffx347TLDzZmTESLqZ8Kf2E8AGOxGeWodCf5y+\r\ncHfOVnIzsFYtOqBSC8cS8RSpNPjij/gdPVDupD1XbRYPH5PZpxV7JQ2Vv84H\r\ngzKWdLDeCY91+ydi4iUrNGlHg59/u8yp5E0=\r\n=f7C+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=16 || 14 >=14.17"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "c37feab189395d94ec62f07e4db2416ea763c265", "scripts": {"snap": "c8 tap", "test": "c8 tap", "bench": "bash ./scripts/bench.sh", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash ./scripts/fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 75, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git+https://github.com/isaacs/path-walker.git", "type": "git"}, "_npmVersion": "9.6.4", "description": "walk paths fast and efficiently", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"minipass": "^5.0.0", "lru-cache": "^9.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.4", "mkdirp": "^3.0.0", "rimraf": "^4.1.2", "ts-node": "^10.9.1", "typedoc": "^0.23.24", "prettier": "^2.8.3", "@types/tap": "^15.0.7", "typescript": "^5.0.4", "@types/node": "^18.11.18", "@nodelib/fs.walk": "^1.2.8", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/path-scurry_1.7.0_1681513230035_0.11427273017785744", "host": "s3://npm-registry-packages"}}, "1.7.1": {"name": "path-scurry", "version": "1.7.1", "author": {"url": "https://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BlueOak-1.0.0", "_id": "path-scurry@1.7.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/path-walker#readme", "bugs": {"url": "https://github.com/isaacs/path-walker/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "daed01c7c44fb39adf1dc6ebb6cf5bc5597e198b", "tarball": "https://registry.npmjs.org/path-scurry/-/path-scurry-1.7.1.tgz", "fileCount": 13, "integrity": "sha512-BEVj/HRB5/uwYu17UX1pOE3N8Zq4v4qBArzCZOytoBEAq+eMS+zZxqVjd8FoUh+PRdc6V+6nFNe96Vjcb8POKg==", "signatures": [{"sig": "MEYCIQDAAJeNydzQHlmVXx+fjsARYTln++5q44eNKQaMnEnkvQIhAOrSdOHUtMs1+3aVnsLs3q86eG5rkVjujuZmI0vWQHdb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 514567}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=16 || 14 >=14.17"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "247efbec70c29d5f32086a1d71e48a980385a94e", "scripts": {"snap": "c8 tap", "test": "c8 tap", "bench": "bash ./scripts/bench.sh", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash ./scripts/fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 75, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git+https://github.com/isaacs/path-walker.git", "type": "git"}, "_npmVersion": "9.6.5", "description": "walk paths fast and efficiently", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"minipass": "^5.0.0", "lru-cache": "^9.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.4", "mkdirp": "^3.0.0", "rimraf": "^4.1.2", "ts-node": "^10.9.1", "typedoc": "^0.23.24", "prettier": "^2.8.3", "@types/tap": "^15.0.7", "typescript": "^5.0.4", "@types/node": "^18.11.18", "@nodelib/fs.walk": "^1.2.8", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/path-scurry_1.7.1_1683733206554_0.42358423890378716", "host": "s3://npm-registry-packages"}}, "1.8.0": {"name": "path-scurry", "version": "1.8.0", "author": {"url": "https://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BlueOak-1.0.0", "_id": "path-scurry@1.8.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/path-walker#readme", "bugs": {"url": "https://github.com/isaacs/path-walker/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "809e09690c63817c76d0183f19a5b21b530ff7d2", "tarball": "https://registry.npmjs.org/path-scurry/-/path-scurry-1.8.0.tgz", "fileCount": 13, "integrity": "sha512-IjTrKseM404/UAWA8bBbL3Qp6O2wXkanuIE3seCxBH7ctRuvH1QRawy1N3nVDHGkdeZsjOsSe/8AQBL/VQCy2g==", "signatures": [{"sig": "MEQCIH11BaZpq2ExtDBjvQTqbq2hOf2aq70+RIeHFE8Mxb/OAiBv/ZviUgJU7DCmaheQxWQ9xPFuYcf5v3JZ4GadMkfTHg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 520899}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=16 || 14 >=14.17"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "04b27633c9b5acdb5c8317f944cb22a600146075", "scripts": {"snap": "c8 tap", "test": "c8 tap", "bench": "bash ./scripts/bench.sh", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash ./scripts/fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 75, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git+https://github.com/isaacs/path-walker.git", "type": "git"}, "_npmVersion": "9.6.5", "description": "walk paths fast and efficiently", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"minipass": "^5.0.0", "lru-cache": "^9.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.4", "mkdirp": "^3.0.0", "rimraf": "^4.1.2", "ts-node": "^10.9.1", "typedoc": "^0.23.24", "prettier": "^2.8.3", "@types/tap": "^15.0.7", "typescript": "^5.0.4", "@types/node": "^18.11.18", "@nodelib/fs.walk": "^1.2.8", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/path-scurry_1.8.0_1683736257520_0.9171206278186692", "host": "s3://npm-registry-packages"}}, "1.9.0": {"name": "path-scurry", "version": "1.9.0", "author": {"url": "https://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BlueOak-1.0.0", "_id": "path-scurry@1.9.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/path-walker#readme", "bugs": {"url": "https://github.com/isaacs/path-walker/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "f8a4f273c28bb73722c11d6872d91572a6a4ec97", "tarball": "https://registry.npmjs.org/path-scurry/-/path-scurry-1.9.0.tgz", "fileCount": 13, "integrity": "sha512-KLejx+14koDZLnb3JNDZbjcmHC/IO2Imd3rNoyt5mynieVd+MT4b1aaGaXAHw06H3P+aZ3Q+56VKJ6BCHMO3WA==", "signatures": [{"sig": "MEUCIDnczQNRl7HShxXXzaQ43rH/RLV5gxb4c7h0gb7mn2AVAiEA3T2r6wfCVNoR+50cNDTGGQV/60QIogzsBw1BtIcLT4A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 523733}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=16 || 14 >=14.17"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "7854c85ab6df77039e40b1f833c4a751fc36bb7a", "scripts": {"snap": "c8 tap", "test": "c8 tap", "bench": "bash ./scripts/bench.sh", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash ./scripts/fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 75, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git+https://github.com/isaacs/path-walker.git", "type": "git"}, "_npmVersion": "9.6.5", "description": "walk paths fast and efficiently", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"minipass": "^5.0.0", "lru-cache": "^9.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.4", "mkdirp": "^3.0.0", "rimraf": "^4.1.2", "ts-node": "^10.9.1", "typedoc": "^0.23.24", "prettier": "^2.8.3", "@types/tap": "^15.0.7", "typescript": "^5.0.4", "@types/node": "^20.1.4", "@nodelib/fs.walk": "^1.2.8", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/path-scurry_1.9.0_1684088817906_0.6740667434474248", "host": "s3://npm-registry-packages"}}, "1.9.1": {"name": "path-scurry", "version": "1.9.1", "author": {"url": "https://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BlueOak-1.0.0", "_id": "path-scurry@1.9.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/path-walker#readme", "bugs": {"url": "https://github.com/isaacs/path-walker/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "838566bb22e38feaf80ecd49ae06cd12acd782ee", "tarball": "https://registry.npmjs.org/path-scurry/-/path-scurry-1.9.1.tgz", "fileCount": 13, "integrity": "sha512-UgmoiySyjFxP6tscZDgWGEAgsW5ok8W3F5CJDnnH2pozwSTGE6eH7vwTotMwATWA2r5xqdkKdxYPkwlJjAI/3g==", "signatures": [{"sig": "MEQCIDmjGa3mppmWyJyTKsjzzwFwyZ1FI5J/gZ6ao0q5B1ycAiAM1lx1k1jYvMIw8vQDd7+Ta98pGD0YKo6XsXaNMAX5hQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 523743}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=16 || 14 >=14.17"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "25f8788570813f2085c00c9679a0bbb532f2a874", "scripts": {"snap": "c8 tap", "test": "c8 tap", "bench": "bash ./scripts/bench.sh", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash ./scripts/fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 75, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git+https://github.com/isaacs/path-walker.git", "type": "git"}, "_npmVersion": "9.6.5", "description": "walk paths fast and efficiently", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"minipass": "^5.0.0 || ^6.0.0", "lru-cache": "^9.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.4", "mkdirp": "^3.0.0", "rimraf": "^4.1.2", "ts-node": "^10.9.1", "typedoc": "^0.23.24", "prettier": "^2.8.3", "@types/tap": "^15.0.7", "typescript": "^5.0.4", "@types/node": "^20.1.4", "@nodelib/fs.walk": "^1.2.8", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/path-scurry_1.9.1_1684125884972_0.06920927016367417", "host": "s3://npm-registry-packages"}}, "1.9.2": {"name": "path-scurry", "version": "1.9.2", "author": {"url": "https://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BlueOak-1.0.0", "_id": "path-scurry@1.9.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/path-walker#readme", "bugs": {"url": "https://github.com/isaacs/path-walker/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "90f9d296ac5e37e608028e28a447b11d385b3f63", "tarball": "https://registry.npmjs.org/path-scurry/-/path-scurry-1.9.2.tgz", "fileCount": 13, "integrity": "sha512-qSDLy2aGFPm8i4rsbHd4MNyTcrzHFsLQykrtbuGRknZZCBBVXSv2tSCDN2Cg6Rt/GFRw8GoW9y9Ecw5rIPG1sg==", "signatures": [{"sig": "MEUCIQD+ZPiCis79TlCuC3kVAeuX92DM4axgj7vK/GVLWb4odgIgX5K6uSQT0ZLpBnucqGc5jSRPEzSicdU1denli1lGOY4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 523743}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=16 || 14 >=14.17"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "e3f947f9341a795d43765f359d13a796053c1619", "scripts": {"snap": "c8 tap", "test": "c8 tap", "bench": "bash ./scripts/bench.sh", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash ./scripts/fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 75, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git+https://github.com/isaacs/path-walker.git", "type": "git"}, "_npmVersion": "9.6.5", "description": "walk paths fast and efficiently", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"minipass": "^5.0.0 || ^6.0.2", "lru-cache": "^9.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.4", "mkdirp": "^3.0.0", "rimraf": "^4.1.2", "ts-node": "^10.9.1", "typedoc": "^0.23.24", "prettier": "^2.8.3", "@types/tap": "^15.0.7", "typescript": "^5.0.4", "@types/node": "^20.1.4", "@nodelib/fs.walk": "^1.2.8", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/path-scurry_1.9.2_1684358793421_0.09033724841311175", "host": "s3://npm-registry-packages"}}, "1.10.0": {"name": "path-scurry", "version": "1.10.0", "author": {"url": "https://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BlueOak-1.0.0", "_id": "path-scurry@1.10.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/path-scurry#readme", "bugs": {"url": "https://github.com/isaacs/path-scurry/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "0ffbd4c1f7de9600f98a1405507d9f9acb438ab3", "tarball": "https://registry.npmjs.org/path-scurry/-/path-scurry-1.10.0.tgz", "fileCount": 13, "integrity": "sha512-tZFEaRQbMLjwrsmidsGJ6wDMv0iazJWk6SfIKnY4Xru8auXgmJkOBa5DUbYFcFD2Rzk2+KDlIiF0GVXNCbgC7g==", "signatures": [{"sig": "MEUCIAl0GacGh5ZPVmiVvjBFz+oSjBblixbXK18Y2JvnGLf4AiEA3u+9XYK8f/f811BgUhpWS3jpDABYY2WA8J6aFgLk3P4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 529046}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=16 || 14 >=14.17"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "8c75663a4a159c13a58e4aedf46e2a444d7dd4b9", "scripts": {"snap": "c8 tap", "test": "c8 tap", "bench": "bash ./scripts/bench.sh", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash ./scripts/fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 75, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git+https://github.com/isaacs/path-scurry.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "walk paths fast and efficiently", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"minipass": "^5.0.0 || ^6.0.2", "lru-cache": "^9.1.1 || ^10.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.4", "mkdirp": "^3.0.0", "rimraf": "^4.1.2", "ts-node": "^10.9.1", "typedoc": "^0.23.24", "prettier": "^2.8.3", "@types/tap": "^15.0.7", "typescript": "^5.0.4", "@types/node": "^20.1.4", "@nodelib/fs.walk": "^1.2.8", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/path-scurry_1.10.0_1687905989660_0.3141284104903457", "host": "s3://npm-registry-packages"}}, "1.10.1": {"name": "path-scurry", "version": "1.10.1", "author": {"url": "https://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BlueOak-1.0.0", "_id": "path-scurry@1.10.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/path-scurry#readme", "bugs": {"url": "https://github.com/isaacs/path-scurry/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "9ba6bf5aa8500fe9fd67df4f0d9483b2b0bfc698", "tarball": "https://registry.npmjs.org/path-scurry/-/path-scurry-1.10.1.tgz", "fileCount": 13, "integrity": "sha512-MkhCqzzBEpPvxxQ71Md0b1Kk51W01lrYvlMzSUaIzNsODdd7mqhiimSZlr+VegAz5Z6Vzt9Xg2ttE//XBhH3EQ==", "signatures": [{"sig": "MEUCIQDoAzuI0u7aZUia1XTF4yYxXthiB/TY30+crnMvM/4edgIgDiAadlS3ccuq/8nhFpSP+D06HlLus10+Ob4FmOL5vWk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 529056}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=16 || 14 >=14.17"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "d4064600dff0b49c2c199ecfa1de0cd51037297b", "scripts": {"snap": "c8 tap", "test": "c8 tap", "bench": "bash ./scripts/bench.sh", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash ./scripts/fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 75, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git+https://github.com/isaacs/path-scurry.git", "type": "git"}, "_npmVersion": "9.7.2", "description": "walk paths fast and efficiently", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"minipass": "^5.0.0 || ^6.0.2 || ^7.0.0", "lru-cache": "^9.1.1 || ^10.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.4", "mkdirp": "^3.0.0", "rimraf": "^5.0.1", "ts-node": "^10.9.1", "typedoc": "^0.23.24", "prettier": "^2.8.3", "@types/tap": "^15.0.7", "typescript": "^5.0.4", "@types/node": "^20.1.4", "@nodelib/fs.walk": "^1.2.8", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/path-scurry_1.10.1_1688775420828_0.47974477278111705", "host": "s3://npm-registry-packages"}}, "1.10.2": {"name": "path-scurry", "version": "1.10.2", "author": {"url": "https://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BlueOak-1.0.0", "_id": "path-scurry@1.10.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/path-scurry#readme", "bugs": {"url": "https://github.com/isaacs/path-scurry/issues"}, "dist": {"shasum": "8f6357eb1239d5fa1da8b9f70e9c080675458ba7", "tarball": "https://registry.npmjs.org/path-scurry/-/path-scurry-1.10.2.tgz", "fileCount": 13, "integrity": "sha512-7xTavNy5RQXnsjANvVvMkEjvloOinkAjv/Z6Ildz9v2RinZ4SBKTWFOVRbaF8p0vpHnyjV/UwNDdKuUv6M5qcA==", "signatures": [{"sig": "MEQCIAvCBKeJZUqYzzruvuh9Wl6GXW2a870nGOJDwRKZshDaAiBkKTFgJSSLzFi+ODjjxwspb7+ptnlmUu32+8+ulv2ILA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 533163}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./package.json": "./package.json"}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "engines": {"node": ">=16 || 14 >=14.17"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "b106ef5f3fd54668a5c91e7757e5056b1b302023", "scripts": {"snap": "tap", "test": "tap", "bench": "bash ./scripts/bench.sh", "format": "prettier --write . --loglevel warn", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "preprepare": "rm -rf dist", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 75, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git+https://github.com/isaacs/path-scurry.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "walk paths fast and efficiently", "directories": {}, "_nodeVersion": "20.11.0", "dependencies": {"minipass": "^5.0.0 || ^6.0.2 || ^7.0.0", "lru-cache": "^10.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^18.7.2", "tshy": "^1.12.0", "mkdirp": "^3.0.0", "rimraf": "^5.0.1", "ts-node": "^10.9.2", "typedoc": "^0.25.12", "prettier": "^2.8.3", "typescript": "^5.4.3", "@types/node": "^20.11.30", "@nodelib/fs.walk": "^1.2.8", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/path-scurry_1.10.2_1711585437991_0.016981719838604192", "host": "s3://npm-registry-packages"}}, "1.10.3": {"name": "path-scurry", "version": "1.10.3", "author": {"url": "https://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BlueOak-1.0.0", "_id": "path-scurry@1.10.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/path-scurry#readme", "bugs": {"url": "https://github.com/isaacs/path-scurry/issues"}, "dist": {"shasum": "2061907ec123d7f3418e28ab9d9749b82633b314", "tarball": "https://registry.npmjs.org/path-scurry/-/path-scurry-1.10.3.tgz", "fileCount": 13, "integrity": "sha512-UtzYIeru0X6hN7YWN26lZ1sKfRh0JX+co1aCqLFeA9LuxVvLzZQs93UO8W7YHChqXP78O5GW5GkN9ATDfxd5AA==", "signatures": [{"sig": "MEUCICR5THU0CLhgDavK9fHOMku7uB3EP+KCS4M3jEzIqiC5AiEA9VUoUqToip0QVqYE+ELCXZb8hQ+6CDnoh7dMsh++Agw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 534073}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./package.json": "./package.json"}, "selfLink": false}, "type": "module", "types": "./dist/commonjs/index.d.ts", "engines": {"node": ">=16 || 14 >=14.17"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "200e9f886958fcf920c5cd879791fba9a23c7b50", "scripts": {"snap": "tap", "test": "tap", "bench": "bash ./scripts/bench.sh", "format": "prettier --write . --loglevel warn", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 75, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git+https://github.com/isaacs/path-scurry.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "walk paths fast and efficiently", "directories": {}, "_nodeVersion": "20.11.0", "dependencies": {"minipass": "^5.0.0 || ^6.0.2 || ^7.0.0", "lru-cache": "^10.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^18.7.2", "tshy": "^1.14.0", "mkdirp": "^3.0.0", "rimraf": "^5.0.1", "ts-node": "^10.9.2", "typedoc": "^0.25.12", "prettier": "^2.8.3", "typescript": "^5.4.3", "@types/node": "^20.12.11", "@nodelib/fs.walk": "^1.2.8", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/path-scurry_1.10.3_1715262328939_0.5676467122641888", "host": "s3://npm-registry-packages"}}, "1.10.4": {"name": "path-scurry", "version": "1.10.4", "author": {"url": "https://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BlueOak-1.0.0", "_id": "path-scurry@1.10.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/path-scurry#readme", "bugs": {"url": "https://github.com/isaacs/path-scurry/issues"}, "dist": {"shasum": "3a5231f3d6cb8ee69a6d24811466ca2be7cca87a", "tarball": "https://registry.npmjs.org/path-scurry/-/path-scurry-1.10.4.tgz", "fileCount": 13, "integrity": "sha512-nYo46tkNDCe4Ti+K4WP/ns2BjywqQMAeAz7r3lqtVkh8A0L9F86Ju2nLIrzFMUDSs1X0lHivbCiKG4eRUK2Z2Q==", "signatures": [{"sig": "MEQCIEBmlFBO5klo4HGJxOjWnMcuGlDUgNJzsamvxQVTYlnlAiBsSf+vM+wE8P2JOiQ1wBoeIoKM3kYIS2Y8vk4yimS/tQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 534224}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./package.json": "./package.json"}, "selfLink": false}, "type": "module", "types": "./dist/commonjs/index.d.ts", "engines": {"node": ">=16 || 14 >=14.17"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "34e0e2d10430758d12cda65d713e259def47aca7", "scripts": {"snap": "tap", "test": "tap", "bench": "bash ./scripts/bench.sh", "format": "prettier --write . --loglevel warn", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 75, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git+https://github.com/isaacs/path-scurry.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "walk paths fast and efficiently", "directories": {}, "_nodeVersion": "20.11.0", "dependencies": {"minipass": "^5.0.0 || ^6.0.2 || ^7.0.0", "lru-cache": "^10.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^18.7.2", "tshy": "^1.14.0", "mkdirp": "^3.0.0", "rimraf": "^5.0.1", "ts-node": "^10.9.2", "typedoc": "^0.25.12", "prettier": "^2.8.3", "typescript": "^5.4.3", "@types/node": "^20.12.11", "@nodelib/fs.walk": "^1.2.8", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/path-scurry_1.10.4_1715262457844_0.16918334413118052", "host": "s3://npm-registry-packages"}}, "1.11.0": {"name": "path-scurry", "version": "1.11.0", "author": {"url": "https://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BlueOak-1.0.0", "_id": "path-scurry@1.11.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/path-scurry#readme", "bugs": {"url": "https://github.com/isaacs/path-scurry/issues"}, "tap": {"typecheck": true}, "dist": {"shasum": "332d64e9726bf667fb348e5a1c71005c09ad741a", "tarball": "https://registry.npmjs.org/path-scurry/-/path-scurry-1.11.0.tgz", "fileCount": 13, "integrity": "sha512-LNHTaVkzaYaLGlO+0u3rQTz7QrHTFOuKyba9JMTQutkmtNew8dw8wOD7mTU/5fCPZzCWpfW0XnQKzY61P0aTaw==", "signatures": [{"sig": "MEUCIQDrOKjPXLRP9GSCSvERJ+fDKtTLxZ1zZncryQV9VH4lvgIgCw1SFQnyOF27nDGl6zvaZn/Orip8Dz20VA+fl9et/T8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 535479}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./package.json": "./package.json"}, "selfLink": false}, "type": "module", "types": "./dist/commonjs/index.d.ts", "engines": {"node": ">=16 || 14 >=14.17"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "281b6dbf5bf2439a8d061280e596905cfb568f10", "scripts": {"snap": "tap", "test": "tap", "bench": "bash ./scripts/bench.sh", "format": "prettier --write . --loglevel warn", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 75, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "experimentalTernaries": true}, "repository": {"url": "git+https://github.com/isaacs/path-scurry.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "walk paths fast and efficiently", "directories": {}, "_nodeVersion": "20.11.0", "dependencies": {"minipass": "^5.0.0 || ^6.0.2 || ^7.0.0", "lru-cache": "^10.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^18.7.2", "tshy": "^1.14.0", "mkdirp": "^3.0.0", "rimraf": "^5.0.1", "ts-node": "^10.9.2", "typedoc": "^0.25.12", "prettier": "^3.2.5", "typescript": "^5.4.3", "@types/node": "^20.12.11", "@nodelib/fs.walk": "^1.2.8", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/path-scurry_1.11.0_1715266205489_0.4694971418652967", "host": "s3://npm-registry-packages"}}, "1.11.1": {"name": "path-scurry", "version": "1.11.1", "author": {"url": "https://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BlueOak-1.0.0", "_id": "path-scurry@1.11.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/path-scurry#readme", "bugs": {"url": "https://github.com/isaacs/path-scurry/issues"}, "tap": {"typecheck": true}, "dist": {"shasum": "7960a668888594a0720b12a911d1a742ab9f11d2", "tarball": "https://registry.npmjs.org/path-scurry/-/path-scurry-1.11.1.tgz", "fileCount": 13, "integrity": "sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==", "signatures": [{"sig": "MEYCIQDBjLbfo2sQti3sfRnzaiinq/kkXl1m+QvSVxxwW4e8xAIhAOU7wQ5HNxGrEROeHVnPM5T/4hhZllwlkzv1Aufgubk8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 535479}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./package.json": "./package.json"}, "selfLink": false}, "type": "module", "types": "./dist/commonjs/index.d.ts", "engines": {"node": ">=16 || 14 >=14.18"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "766c9b06aacef86b43c9999cabc5eb824b5958d6", "scripts": {"snap": "tap", "test": "tap", "bench": "bash ./scripts/bench.sh", "format": "prettier --write . --loglevel warn", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 75, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "experimentalTernaries": true}, "repository": {"url": "git+https://github.com/isaacs/path-scurry.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "walk paths fast and efficiently", "directories": {}, "_nodeVersion": "20.11.0", "dependencies": {"minipass": "^5.0.0 || ^6.0.2 || ^7.0.0", "lru-cache": "^10.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^18.7.2", "tshy": "^1.14.0", "mkdirp": "^3.0.0", "rimraf": "^5.0.1", "ts-node": "^10.9.2", "typedoc": "^0.25.12", "prettier": "^3.2.5", "typescript": "^5.4.3", "@types/node": "^20.12.11", "@nodelib/fs.walk": "^1.2.8", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/path-scurry_1.11.1_1715482040616_0.19616026688873078", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "path-scurry", "version": "2.0.0", "description": "walk paths fast and efficiently", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://blog.izs.me"}, "main": "./dist/commonjs/index.js", "type": "module", "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "license": "BlueOak-1.0.0", "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "prepare": "tshy", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "tap", "snap": "tap", "format": "prettier --write . --log-level warn", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "bench": "bash ./scripts/bench.sh"}, "prettier": {"experimentalTernaries": true, "semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "devDependencies": {"@nodelib/fs.walk": "^2.0.0", "@types/node": "^20.14.10", "mkdirp": "^3.0.0", "prettier": "^3.3.2", "rimraf": "^5.0.8", "tap": "^20.0.3", "ts-node": "^10.9.2", "tshy": "^2.0.1", "typedoc": "^0.26.3", "typescript": "^5.5.3"}, "tap": {"typecheck": true}, "engines": {"node": "20 || >=22"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/path-scurry.git"}, "dependencies": {"lru-cache": "^11.0.0", "minipass": "^7.1.2"}, "tshy": {"selfLink": false, "exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "types": "./dist/commonjs/index.d.ts", "module": "./dist/esm/index.js", "_id": "path-scurry@2.0.0", "gitHead": "8290d909be8d91989747c2794510a9df6cd74c7e", "bugs": {"url": "https://github.com/isaacs/path-scurry/issues"}, "homepage": "https://github.com/isaacs/path-scurry#readme", "_nodeVersion": "20.13.1", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-ypGJsmGtdXUOeM5u93TyeIEfEhM6s+ljAhrk5vAvSx8uyY/02OvrZnA0YNGUrPXfpJMgI1ODd3nwz8Npx4O4cg==", "shasum": "9f052289f23ad8bf9397a2a0425e7b8615c58580", "tarball": "https://registry.npmjs.org/path-scurry/-/path-scurry-2.0.0.tgz", "fileCount": 13, "unpackedSize": 535406, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDyWFYPZsi0MocNbjahmj23VwiVSjOIa/XKvdV3StQ5EgIhAOJBaAAlBnipKRFdcMGi91JxE2KTbOupQMpQuXcka5+I"}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/path-scurry_2.0.0_1720476405183_0.1506416196278888"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-02-07T05:05:40.075Z", "modified": "2024-07-08T22:06:45.534Z", "0.0.0-0": "2023-02-07T05:05:40.334Z", "1.0.0": "2023-02-07T05:15:56.753Z", "1.0.1": "2023-02-07T05:17:31.309Z", "1.1.0": "2023-02-07T21:47:20.011Z", "1.1.1": "2023-02-08T20:19:45.947Z", "1.2.0": "2023-02-09T19:01:01.835Z", "1.3.0": "2023-02-12T04:51:56.217Z", "1.4.0": "2023-02-13T16:51:32.302Z", "1.5.0": "2023-02-27T05:51:44.714Z", "1.6.0": "2023-03-01T21:56:20.111Z", "1.6.1": "2023-03-01T22:12:52.205Z", "1.6.2": "2023-03-22T05:30:58.343Z", "1.6.3": "2023-03-22T18:50:47.648Z", "1.6.4": "2023-04-09T22:06:57.282Z", "1.7.0": "2023-04-14T23:00:30.211Z", "1.7.1": "2023-05-10T15:40:06.773Z", "1.8.0": "2023-05-10T16:30:57.689Z", "1.9.0": "2023-05-14T18:26:58.094Z", "1.9.1": "2023-05-15T04:44:45.154Z", "1.9.2": "2023-05-17T21:26:33.609Z", "1.10.0": "2023-06-27T22:46:29.832Z", "1.10.1": "2023-07-08T00:17:01.051Z", "1.10.2": "2024-03-28T00:23:58.236Z", "1.10.3": "2024-05-09T13:45:29.124Z", "1.10.4": "2024-05-09T13:47:38.006Z", "1.11.0": "2024-05-09T14:50:05.664Z", "1.11.1": "2024-05-12T02:47:20.787Z", "2.0.0": "2024-07-08T22:06:45.331Z"}, "bugs": {"url": "https://github.com/isaacs/path-scurry/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://blog.izs.me"}, "license": "BlueOak-1.0.0", "homepage": "https://github.com/isaacs/path-scurry#readme", "repository": {"type": "git", "url": "git+https://github.com/isaacs/path-scurry.git"}, "description": "walk paths fast and efficiently", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "readme": "# path-scurry\n\nExtremely high performant utility for building tools that read\nthe file system, minimizing filesystem and path string munging\noperations to the greatest degree possible.\n\n## Ugh, yet another file traversal thing on npm?\n\nYes. None of the existing ones gave me exactly what I wanted.\n\n## Well what is it you wanted?\n\nWhile working on [glob](http://npm.im/glob), I found that I\nneeded a module to very efficiently manage the traversal over a\nfolder tree, such that:\n\n1. No `readdir()` or `stat()` would ever be called on the same\n   file or directory more than one time.\n2. No `readdir()` calls would be made if we can be reasonably\n   sure that the path is not a directory. (Ie, a previous\n   `readdir()` or `stat()` covered the path, and\n   `ent.isDirectory()` is false.)\n3. `path.resolve()`, `dirname()`, `basename()`, and other\n   string-parsing/munging operations are be minimized. This means\n   it has to track \"provisional\" child nodes that may not exist\n   (and if we find that they _don't_ exist, store that\n   information as well, so we don't have to ever check again).\n4. The API is not limited to use as a stream/iterator/etc. There\n   are many cases where an API like node's `fs` is preferrable.\n5. It's more important to prevent excess syscalls than to be up\n   to date, but it should be smart enough to know what it\n   _doesn't_ know, and go get it seamlessly when requested.\n6. Do not blow up the JS heap allocation if operating on a\n   directory with a huge number of entries.\n7. Handle all the weird aspects of Windows paths, like UNC paths\n   and drive letters and wrongway slashes, so that the consumer\n   can return canonical platform-specific paths without having to\n   parse or join or do any error-prone string munging.\n\n## PERFORMANCE\n\nJavaScript people throw around the word \"blazing\" a lot. I hope\nthat this module doesn't blaze anyone. But it does go very fast,\nin the cases it's optimized for, if used properly.\n\nPathScurry provides ample opportunities to get extremely good\nperformance, as well as several options to trade performance for\nconvenience.\n\nBenchmarks can be run by executing `npm run bench`.\n\nAs is always the case, doing more means going slower, doing less\nmeans going faster, and there are trade offs between speed and\nmemory usage.\n\nPathScurry makes heavy use of [LRUCache](http://npm.im/lru-cache)\nto efficiently cache whatever it can, and `Path` objects remain\nin the graph for the lifetime of the walker, so repeated calls\nwith a single PathScurry object will be extremely fast. However,\nadding items to a cold cache means \"doing more\", so in those\ncases, we pay a price. Nothing is free, but every effort has been\nmade to reduce costs wherever possible.\n\nAlso, note that a \"cache as long as possible\" approach means that\nchanges to the filesystem may not be reflected in the results of\nrepeated PathScurry operations.\n\nFor resolving string paths, `PathScurry` ranges from 5-50 times\nfaster than `path.resolve` on repeated resolutions, but around\n100 to 1000 times _slower_ on the first resolution. If your\nprogram is spending a lot of time resolving the _same_ paths\nrepeatedly (like, thousands or millions of times), then this can\nbe beneficial. But both implementations are pretty fast, and\nspeeding up an infrequent operation from 4µs to 400ns is not\ngoing to move the needle on your app's performance.\n\nFor walking file system directory trees, a lot depends on how\noften a given PathScurry object will be used, and also on the\nwalk method used.\n\nWith default settings on a folder tree of 100,000 items,\nconsisting of around a 10-to-1 ratio of normal files to\ndirectories, PathScurry performs comparably to\n[@nodelib/fs.walk](http://npm.im/@nodelib/fs.walk), which is the\nfastest and most reliable file system walker I could find. As far\nas I can tell, it's almost impossible to go much faster in a\nNode.js program, just based on how fast you can push syscalls out\nto the fs thread pool.\n\nOn my machine, that is about 1000-1200 completed walks per second\nfor async or stream walks, and around 500-600 walks per second\nsynchronously.\n\nIn the warm cache state, PathScurry's performance increases\naround 4x for async `for await` iteration, 10-15x faster for\nstreams and synchronous `for of` iteration, and anywhere from 30x\nto 80x faster for the rest.\n\n```\n# walk 100,000 fs entries, 10/1 file/dir ratio\n# operations / ms\n New PathScurry object  |  Reuse PathScurry object\n     stream:  1112.589  |  13974.917\nsync stream:   492.718  |  15028.343\n async walk:  1095.648  |  32706.395\n  sync walk:   527.632  |  46129.772\n async iter:  1288.821  |   5045.510\n  sync iter:   498.496  |  17920.746\n```\n\nA hand-rolled walk calling `entry.readdir()` and recursing\nthrough the entries can benefit even more from caching, with\ngreater flexibility and without the overhead of streams or\ngenerators.\n\nThe cold cache state is still limited by the costs of file system\noperations, but with a warm cache, the only bottleneck is CPU\nspeed and VM optimizations. Of course, in that case, some care\nmust be taken to ensure that you don't lose performance as a\nresult of silly mistakes, like calling `readdir()` on entries\nthat you know are not directories.\n\n```\n# manual recursive iteration functions\n      cold cache  |  warm cache\nasync:  1164.901  |  17923.320\n   cb:  1101.127  |  40999.344\nzalgo:  1082.240  |  66689.936\n sync:   526.935  |  87097.591\n```\n\nIn this case, the speed improves by around 10-20x in the async\ncase, 40x in the case of using `entry.readdirCB` with protections\nagainst synchronous callbacks, and 50-100x with callback\ndeferrals disabled, and _several hundred times faster_ for\nsynchronous iteration.\n\nIf you can think of a case that is not covered in these\nbenchmarks, or an implementation that performs significantly\nbetter than PathScurry, please [let me\nknow](https://github.com/isaacs/path-scurry/issues).\n\n## USAGE\n\n```ts\n// hybrid module, load with either method\nimport { PathScurry, Path } from 'path-scurry'\n// or:\nconst { PathScurry, Path } = require('path-scurry')\n\n// very simple example, say we want to find and\n// delete all the .DS_Store files in a given path\n// note that the API is very similar to just a\n// naive walk with fs.readdir()\nimport { unlink } from 'fs/promises'\n\n// easy way, iterate over the directory and do the thing\nconst pw = new PathScurry(process.cwd())\nfor await (const entry of pw) {\n  if (entry.isFile() && entry.name === '.DS_Store') {\n    unlink(entry.fullpath())\n  }\n}\n\n// here it is as a manual recursive method\nconst walk = async (entry: Path) => {\n  const promises: Promise<any> = []\n  // readdir doesn't throw on non-directories, it just doesn't\n  // return any entries, to save stack trace costs.\n  // Items are returned in arbitrary unsorted order\n  for (const child of await pw.readdir(entry)) {\n    // each child is a Path object\n    if (child.name === '.DS_Store' && child.isFile()) {\n      // could also do pw.resolve(entry, child.name),\n      // just like fs.readdir walking, but .fullpath is\n      // a *slightly* more efficient shorthand.\n      promises.push(unlink(child.fullpath()))\n    } else if (child.isDirectory()) {\n      promises.push(walk(child))\n    }\n  }\n  return Promise.all(promises)\n}\n\nwalk(pw.cwd).then(() => {\n  console.log('all .DS_Store files removed')\n})\n\nconst pw2 = new PathScurry('/a/b/c') // pw2.cwd is the Path for /a/b/c\nconst relativeDir = pw2.cwd.resolve('../x') // Path entry for '/a/b/x'\nconst relative2 = pw2.cwd.resolve('/a/b/d/../x') // same path, same entry\nassert.equal(relativeDir, relative2)\n```\n\n## API\n\n[Full TypeDoc API](https://isaacs.github.io/path-scurry)\n\nThere are platform-specific classes exported, but for the most\npart, the default `PathScurry` and `Path` exports are what you\nmost likely need, unless you are testing behavior for other\nplatforms.\n\nIntended public API is documented here, but the full\ndocumentation does include internal types, which should not be\naccessed directly.\n\n### Interface `PathScurryOpts`\n\nThe type of the `options` argument passed to the `PathScurry`\nconstructor.\n\n- `nocase`: Boolean indicating that file names should be compared\n  case-insensitively. Defaults to `true` on darwin and win32\n  implementations, `false` elsewhere.\n\n  **Warning** Performing case-insensitive matching on a\n  case-sensitive filesystem will result in occasionally very\n  bizarre behavior. Performing case-sensitive matching on a\n  case-insensitive filesystem may negatively impact performance.\n\n- `childrenCacheSize`: Number of child entries to cache, in order\n  to speed up `resolve()` and `readdir()` calls. Defaults to\n  `16 * 1024` (ie, `16384`).\n\n  Setting it to a higher value will run the risk of JS heap\n  allocation errors on large directory trees. Setting it to `256`\n  or smaller will significantly reduce the construction time and\n  data consumption overhead, but with the downside of operations\n  being slower on large directory trees. Setting it to `0` will\n  mean that effectively no operations are cached, and this module\n  will be roughly the same speed as `fs` for file system\n  operations, and _much_ slower than `path.resolve()` for\n  repeated path resolution.\n\n- `fs` An object that will be used to override the default `fs`\n  methods. Any methods that are not overridden will use Node's\n  built-in implementations.\n\n  - lstatSync\n  - readdir (callback `withFileTypes` Dirent variant, used for\n    readdirCB and most walks)\n  - readdirSync\n  - readlinkSync\n  - realpathSync\n  - promises: Object containing the following async methods:\n    - lstat\n    - readdir (Dirent variant only)\n    - readlink\n    - realpath\n\n### Interface `WalkOptions`\n\nThe options object that may be passed to all walk methods.\n\n- `withFileTypes`: Boolean, default true. Indicates that `Path`\n  objects should be returned. Set to `false` to get string paths\n  instead.\n- `follow`: Boolean, default false. Attempt to read directory\n  entries from symbolic links. Otherwise, only actual directories\n  are traversed. Regardless of this setting, a given target path\n  will only ever be walked once, meaning that a symbolic link to\n  a previously traversed directory will never be followed.\n\n  Setting this imposes a slight performance penalty, because\n  `readlink` must be called on all symbolic links encountered, in\n  order to avoid infinite cycles.\n\n- `filter`: Function `(entry: Path) => boolean`. If provided,\n  will prevent the inclusion of any entry for which it returns a\n  falsey value. This will not prevent directories from being\n  traversed if they do not pass the filter, though it will\n  prevent the directories themselves from being included in the\n  results. By default, if no filter is provided, then all entries\n  are included in the results.\n- `walkFilter`: Function `(entry: Path) => boolean`. If provided,\n  will prevent the traversal of any directory (or in the case of\n  `follow:true` symbolic links to directories) for which the\n  function returns false. This will not prevent the directories\n  themselves from being included in the result set. Use `filter`\n  for that.\n\nNote that TypeScript return types will only be inferred properly\nfrom static analysis if the `withFileTypes` option is omitted, or\na constant `true` or `false` value.\n\n### Class `PathScurry`\n\nThe main interface. Defaults to an appropriate class based on the\ncurrent platform.\n\nUse `PathScurryWin32`, `PathScurryDarwin`, or `PathScurryPosix`\nif implementation-specific behavior is desired.\n\nAll walk methods may be called with a `WalkOptions` argument to\nwalk over the object's current working directory with the\nsupplied options.\n\n#### `async pw.walk(entry?: string | Path | WalkOptions, opts?: WalkOptions)`\n\nWalk the directory tree according to the options provided,\nresolving to an array of all entries found.\n\n#### `pw.walkSync(entry?: string | Path | WalkOptions, opts?: WalkOptions)`\n\nWalk the directory tree according to the options provided,\nreturning an array of all entries found.\n\n#### `pw.iterate(entry?: string | Path | WalkOptions, opts?: WalkOptions)`\n\nIterate over the directory asynchronously, for use with `for\nawait of`. This is also the default async iterator method.\n\n#### `pw.iterateSync(entry?: string | Path | WalkOptions, opts?: WalkOptions)`\n\nIterate over the directory synchronously, for use with `for of`.\nThis is also the default sync iterator method.\n\n#### `pw.stream(entry?: string | Path | WalkOptions, opts?: WalkOptions)`\n\nReturn a [Minipass](http://npm.im/minipass) stream that emits\neach entry or path string in the walk. Results are made available\nasynchronously.\n\n#### `pw.streamSync(entry?: string | Path | WalkOptions, opts?: WalkOptions)`\n\nReturn a [Minipass](http://npm.im/minipass) stream that emits\neach entry or path string in the walk. Results are made available\nsynchronously, meaning that the walk will complete in a single\ntick if the stream is fully consumed.\n\n#### `pw.cwd`\n\nPath object representing the current working directory for the\nPathScurry.\n\n#### `pw.chdir(path: string)`\n\nSet the new effective current working directory for the scurry\nobject, so that `path.relative()` and `path.relativePosix()`\nreturn values relative to the new cwd path.\n\n#### `pw.depth(path?: Path | string): number`\n\nReturn the depth of the specified path (or the PathScurry cwd)\nwithin the directory tree.\n\nRoot entries have a depth of `0`.\n\n#### `pw.resolve(...paths: string[])`\n\nCaching `path.resolve()`.\n\nSignificantly faster than `path.resolve()` if called repeatedly\nwith the same paths. Significantly slower otherwise, as it builds\nout the cached Path entries.\n\nTo get a `Path` object resolved from the `PathScurry`, use\n`pw.cwd.resolve(path)`. Note that `Path.resolve` only takes a\nsingle string argument, not multiple.\n\n#### `pw.resolvePosix(...paths: string[])`\n\nCaching `path.resolve()`, but always using posix style paths.\n\nThis is identical to `pw.resolve(...paths)` on posix systems (ie,\neverywhere except Windows).\n\nOn Windows, it returns the full absolute UNC path using `/`\nseparators. Ie, instead of `'C:\\\\foo\\\\bar`, it would return\n`//?/C:/foo/bar`.\n\n#### `pw.relative(path: string | Path): string`\n\nReturn the relative path from the PathWalker cwd to the supplied\npath string or entry.\n\nIf the nearest common ancestor is the root, then an absolute path\nis returned.\n\n#### `pw.relativePosix(path: string | Path): string`\n\nReturn the relative path from the PathWalker cwd to the supplied\npath string or entry, using `/` path separators.\n\nIf the nearest common ancestor is the root, then an absolute path\nis returned.\n\nOn posix platforms (ie, all platforms except Windows), this is\nidentical to `pw.relative(path)`.\n\nOn Windows systems, it returns the resulting string as a\n`/`-delimited path. If an absolute path is returned (because the\ntarget does not share a common ancestor with `pw.cwd`), then a\nfull absolute UNC path will be returned. Ie, instead of\n`'C:\\\\foo\\\\bar`, it would return `//?/C:/foo/bar`.\n\n#### `pw.basename(path: string | Path): string`\n\nReturn the basename of the provided string or Path.\n\n#### `pw.dirname(path: string | Path): string`\n\nReturn the parent directory of the supplied string or Path.\n\n#### `async pw.readdir(dir = pw.cwd, opts = { withFileTypes: true })`\n\nRead the directory and resolve to an array of strings if\n`withFileTypes` is explicitly set to `false` or Path objects\notherwise.\n\nCan be called as `pw.readdir({ withFileTypes: boolean })` as\nwell.\n\nReturns `[]` if no entries are found, or if any error occurs.\n\nNote that TypeScript return types will only be inferred properly\nfrom static analysis if the `withFileTypes` option is omitted, or\na constant `true` or `false` value.\n\n#### `pw.readdirSync(dir = pw.cwd, opts = { withFileTypes: true })`\n\nSynchronous `pw.readdir()`\n\n#### `async pw.readlink(link = pw.cwd, opts = { withFileTypes: false })`\n\nCall `fs.readlink` on the supplied string or Path object, and\nreturn the result.\n\nCan be called as `pw.readlink({ withFileTypes: boolean })` as\nwell.\n\nReturns `undefined` if any error occurs (for example, if the\nargument is not a symbolic link), or a `Path` object if\n`withFileTypes` is explicitly set to `true`, or a string\notherwise.\n\nNote that TypeScript return types will only be inferred properly\nfrom static analysis if the `withFileTypes` option is omitted, or\na constant `true` or `false` value.\n\n#### `pw.readlinkSync(link = pw.cwd, opts = { withFileTypes: false })`\n\nSynchronous `pw.readlink()`\n\n#### `async pw.lstat(entry = pw.cwd)`\n\nCall `fs.lstat` on the supplied string or Path object, and fill\nin as much information as possible, returning the updated `Path`\nobject.\n\nReturns `undefined` if the entry does not exist, or if any error\nis encountered.\n\nNote that some `Stats` data (such as `ino`, `dev`, and `mode`)\nwill not be supplied. For those things, you'll need to call\n`fs.lstat` yourself.\n\n#### `pw.lstatSync(entry = pw.cwd)`\n\nSynchronous `pw.lstat()`\n\n#### `pw.realpath(entry = pw.cwd, opts = { withFileTypes: false })`\n\nCall `fs.realpath` on the supplied string or Path object, and\nreturn the realpath if available.\n\nReturns `undefined` if any error occurs.\n\nMay be called as `pw.realpath({ withFileTypes: boolean })` to run\non `pw.cwd`.\n\n#### `pw.realpathSync(entry = pw.cwd, opts = { withFileTypes: false })`\n\nSynchronous `pw.realpath()`\n\n### Class `Path` implements [fs.Dirent](https://nodejs.org/docs/latest/api/fs.html#class-fsdirent)\n\nObject representing a given path on the filesystem, which may or\nmay not exist.\n\nNote that the actual class in use will be either `PathWin32` or\n`PathPosix`, depending on the implementation of `PathScurry` in\nuse. They differ in the separators used to split and join path\nstrings, and the handling of root paths.\n\nIn `PathPosix` implementations, paths are split and joined using\nthe `'/'` character, and `'/'` is the only root path ever in use.\n\nIn `PathWin32` implementations, paths are split using either\n`'/'` or `'\\\\'` and joined using `'\\\\'`, and multiple roots may\nbe in use based on the drives and UNC paths encountered. UNC\npaths such as `//?/C:/` that identify a drive letter, will be\ntreated as an alias for the same root entry as their associated\ndrive letter (in this case `'C:\\\\'`).\n\n#### `path.name`\n\nName of this file system entry.\n\n**Important**: _always_ test the path name against any test\nstring using the `isNamed` method, and not by directly comparing\nthis string. Otherwise, unicode path strings that the system sees\nas identical will not be properly treated as the same path,\nleading to incorrect behavior and possible security issues.\n\n#### `path.isNamed(name: string): boolean`\n\nReturn true if the path is a match for the given path name. This\nhandles case sensitivity and unicode normalization.\n\nNote: even on case-sensitive systems, it is **not** safe to test\nthe equality of the `.name` property to determine whether a given\npathname matches, due to unicode normalization mismatches.\n\nAlways use this method instead of testing the `path.name`\nproperty directly.\n\n#### `path.isCWD`\n\nSet to true if this `Path` object is the current working\ndirectory of the `PathScurry` collection that contains it.\n\n#### `path.getType()`\n\nReturns the type of the Path object, `'File'`, `'Directory'`,\netc.\n\n#### `path.isType(t: type)`\n\nReturns true if `is{t}()` returns true.\n\nFor example, `path.isType('Directory')` is equivalent to\n`path.isDirectory()`.\n\n#### `path.depth()`\n\nReturn the depth of the Path entry within the directory tree.\nRoot paths have a depth of `0`.\n\n#### `path.fullpath()`\n\nThe fully resolved path to the entry.\n\n#### `path.fullpathPosix()`\n\nThe fully resolved path to the entry, using `/` separators.\n\nOn posix systems, this is identical to `path.fullpath()`. On\nwindows, this will return a fully resolved absolute UNC path\nusing `/` separators. Eg, instead of `'C:\\\\foo\\\\bar'`, it will\nreturn `'//?/C:/foo/bar'`.\n\n#### `path.isFile()`, `path.isDirectory()`, etc.\n\nSame as the identical `fs.Dirent.isX()` methods.\n\n#### `path.isUnknown()`\n\nReturns true if the path's type is unknown. Always returns true\nwhen the path is known to not exist.\n\n#### `path.resolve(p: string)`\n\nReturn a `Path` object associated with the provided path string\nas resolved from the current Path object.\n\n#### `path.relative(): string`\n\nReturn the relative path from the PathWalker cwd to the supplied\npath string or entry.\n\nIf the nearest common ancestor is the root, then an absolute path\nis returned.\n\n#### `path.relativePosix(): string`\n\nReturn the relative path from the PathWalker cwd to the supplied\npath string or entry, using `/` path separators.\n\nIf the nearest common ancestor is the root, then an absolute path\nis returned.\n\nOn posix platforms (ie, all platforms except Windows), this is\nidentical to `pw.relative(path)`.\n\nOn Windows systems, it returns the resulting string as a\n`/`-delimited path. If an absolute path is returned (because the\ntarget does not share a common ancestor with `pw.cwd`), then a\nfull absolute UNC path will be returned. Ie, instead of\n`'C:\\\\foo\\\\bar`, it would return `//?/C:/foo/bar`.\n\n#### `async path.readdir()`\n\nReturn an array of `Path` objects found by reading the associated\npath entry.\n\nIf path is not a directory, or if any error occurs, returns `[]`,\nand marks all children as provisional and non-existent.\n\n#### `path.readdirSync()`\n\nSynchronous `path.readdir()`\n\n#### `async path.readlink()`\n\nReturn the `Path` object referenced by the `path` as a symbolic\nlink.\n\nIf the `path` is not a symbolic link, or any error occurs,\nreturns `undefined`.\n\n#### `path.readlinkSync()`\n\nSynchronous `path.readlink()`\n\n#### `async path.lstat()`\n\nCall `lstat` on the path object, and fill it in with details\ndetermined.\n\nIf path does not exist, or any other error occurs, returns\n`undefined`, and marks the path as \"unknown\" type.\n\n#### `path.lstatSync()`\n\nSynchronous `path.lstat()`\n\n#### `async path.realpath()`\n\nCall `realpath` on the path, and return a Path object\ncorresponding to the result, or `undefined` if any error occurs.\n\n#### `path.realpathSync()`\n\nSynchornous `path.realpath()`\n", "readmeFilename": "README.md"}