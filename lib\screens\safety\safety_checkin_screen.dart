import 'package:flutter/material.dart';
import '../../design_system/app_theme.dart';
import '../../design_system/components/reusable_components.dart';

class SafetyCheckinScreen extends StatelessWidget {
  const SafetyCheckinScreen({super.key});

  static String routeName = 'safety-checkin';
  static String routePath = '/safety/safety-checkin';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Safety Check-in'),
        backgroundColor: AppTheme.primaryNavy,
        foregroundColor: AppTheme.white,
      ),
      body: const Padding(
        padding: EdgeInsets.all(AppTheme.spacingMd),
        child: Column(
          children: [
            JJEmptyState(
              title: 'Safety Check-in',
              subtitle: 'Daily safety check-in and hazard assessment',
              icon: Icons.check_circle,
            ),
          ],
        ),
      ),
    );
  }
}
