{"_id": "search1api-mcp", "_rev": "9-4ac4883454bdb541bf37409b1aa436d3", "name": "search1api-mcp", "dist-tags": {"latest": "0.1.8"}, "versions": {"0.1.0": {"name": "search1api-mcp", "version": "0.1.0", "keywords": ["mcp", "claude", "search", "search1api"], "author": {"name": "fatwang2"}, "license": "MIT", "_id": "search1api-mcp@0.1.0", "maintainers": [{"name": "fatwang2", "email": "<EMAIL>"}], "homepage": "https://github.com/fatwang2/search1api-mcp#readme", "bugs": {"url": "https://github.com/fatwang2/search1api-mcp/issues"}, "bin": {"search1api-mcp": "build/index.js"}, "dist": {"shasum": "0e3b773fe6a800c7d2bba26b5e48d75bfe40b4f6", "tarball": "https://registry.npmjs.org/search1api-mcp/-/search1api-mcp-0.1.0.tgz", "fileCount": 4, "integrity": "sha512-+eH+ZYeBGovfDczcTFNg4ZlwArNOLvV7LZX2Rd4ABPCMmzCpKdukgk+ZYZ3lgzKGu6jJRXmUG9YDF9kCfu2xBA==", "signatures": [{"sig": "MEYCIQDOVwxCEb6z9n9ty/iBHigGGagl3YXFn0ig2RHTmreungIhAOulLrHq9nE+fyfAvHGIgI4TaqdxYEhMoUadG6x8CG/g", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8456}, "type": "module", "engines": {"node": ">=18.0.0"}, "private": false, "scripts": {"build": "tsc && node -e \"require('fs').chmodSync('build/index.js', '755')\"", "watch": "tsc --watch", "prepare": "npm run build", "inspector": "npx @modelcontextprotocol/inspector build/index.js"}, "_npmUser": {"name": "fatwang2", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/fatwang2/search1api-mcp.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "A Model Context Protocol (MCP) server that provides search functionality using Search1API", "directories": {}, "_nodeVersion": "23.1.0", "dependencies": {"axios": "^1.7.8", "dotenv": "^16.4.5", "@modelcontextprotocol/sdk": "0.6.0"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.3.3", "@types/node": "^20.11.24"}, "_npmOperationalInternal": {"tmp": "tmp/search1api-mcp_0.1.0_1732611286252_0.8916853719373963", "host": "s3://npm-registry-packages"}}, "0.1.1": {"name": "search1api-mcp", "version": "0.1.1", "keywords": ["mcp", "claude", "search", "crawl", "google", "search1api"], "author": {"name": "fatwang2"}, "license": "MIT", "_id": "search1api-mcp@0.1.1", "maintainers": [{"name": "fatwang2", "email": "<EMAIL>"}], "homepage": "https://github.com/fatwang2/search1api-mcp#readme", "bugs": {"url": "https://github.com/fatwang2/search1api-mcp/issues"}, "bin": {"search1api-mcp": "build/index.js"}, "dist": {"shasum": "8e7fd0cfb1ea206b61228f89d1cc40aca7d73734", "tarball": "https://registry.npmjs.org/search1api-mcp/-/search1api-mcp-0.1.1.tgz", "fileCount": 4, "integrity": "sha512-X5mz+CrnHiprpE1wAwTqJslZRU1i1zw0EcQfscaQLLfEUp+PytU5Mq2Dm0I3Kj5Ixx82xVhDp5G2mP/73rNPBw==", "signatures": [{"sig": "MEQCIBy8WgHpBjvnVfbYieAYRIUab6PJ0YgNhhwUe4Q9YkWOAiBaaEj/iqzXJ+atA/OmHIk5nZCnQqw+u5ShymFsRav0lg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12732}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "e82f83d7f0ed27bbcd51a72108fceead99336083", "private": false, "scripts": {"build": "tsc && node -e \"require('fs').chmodSync('build/index.js', '755')\"", "watch": "tsc --watch", "prepare": "npm run build", "inspector": "npx @modelcontextprotocol/inspector build/index.js"}, "_npmUser": {"name": "fatwang2", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/fatwang2/search1api-mcp.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "A Model Context Protocol (MCP) server that provides search and crawl functionality using Search1API", "directories": {}, "_nodeVersion": "23.1.0", "dependencies": {"axios": "^1.7.8", "dotenv": "^16.4.5", "@modelcontextprotocol/sdk": "0.6.0"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.3.3", "@types/node": "^20.11.24"}, "_npmOperationalInternal": {"tmp": "tmp/search1api-mcp_0.1.1_1732617245736_0.08174244020541077", "host": "s3://npm-registry-packages"}}, "0.1.2": {"name": "search1api-mcp", "version": "0.1.2", "keywords": ["mcp", "claude", "search", "crawl", "google", "search1api"], "author": {"name": "fatwang2"}, "license": "MIT", "_id": "search1api-mcp@0.1.2", "maintainers": [{"name": "fatwang2", "email": "<EMAIL>"}], "homepage": "https://github.com/fatwang2/search1api-mcp#readme", "bugs": {"url": "https://github.com/fatwang2/search1api-mcp/issues"}, "bin": {"search1api-mcp": "build/index.js"}, "dist": {"shasum": "d090afe8e442cec43dd4c60104985b5a6e4b8eef", "tarball": "https://registry.npmjs.org/search1api-mcp/-/search1api-mcp-0.1.2.tgz", "fileCount": 5, "integrity": "sha512-DnDiD++Aiay6yVu9Xyg80vidp5lKSmbBIwCbmw69i3wzF44kZPJc6JroDx4GvyyngKNzGQ0jXPSBh1xUhSaQPQ==", "signatures": [{"sig": "MEUCIDGIKAW0E957zzNVrRjaxhVv8xmFjxs94CHs3zYrml6XAiEAjiSwZ7SgzUpOKI6uXskG8Q0Vhq71Fbmrnes3eRzjI90=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15322}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "4c8ae5488371b88c31e44b67633fad626bbd2c61", "private": false, "scripts": {"build": "tsc && node -e \"require('fs').chmodSync('build/index.js', '755')\"", "watch": "tsc --watch", "prepare": "npm run build", "inspector": "npx @modelcontextprotocol/inspector build/index.js"}, "_npmUser": {"name": "fatwang2", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/fatwang2/search1api-mcp.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "A Model Context Protocol (MCP) server that provides search and crawl functionality using Search1API", "directories": {}, "_nodeVersion": "23.1.0", "dependencies": {"axios": "^1.7.8", "dotenv": "^16.4.5", "@modelcontextprotocol/sdk": "0.6.0"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.3.3", "@types/node": "^20.11.24"}, "_npmOperationalInternal": {"tmp": "tmp/search1api-mcp_0.1.2_1732840661654_0.9807092930392003", "host": "s3://npm-registry-packages"}}, "0.1.3": {"name": "search1api-mcp", "version": "0.1.3", "keywords": ["mcp", "claude", "search", "crawl", "google", "search1api"], "author": {"name": "fatwang2"}, "license": "MIT", "_id": "search1api-mcp@0.1.3", "maintainers": [{"name": "fatwang2", "email": "<EMAIL>"}], "homepage": "https://github.com/fatwang2/search1api-mcp#readme", "bugs": {"url": "https://github.com/fatwang2/search1api-mcp/issues"}, "bin": {"search1api-mcp": "build/index.js"}, "dist": {"shasum": "87fe3d103538f4beeba42223abd036fe4bafba0c", "tarball": "https://registry.npmjs.org/search1api-mcp/-/search1api-mcp-0.1.3.tgz", "fileCount": 5, "integrity": "sha512-KofhBZyM3ggwWWNBMO4KENNAciTAndWVal4bs5VED69NPXAKa/RYMVZzE6zxeUdbN+ho7SObPfqn4uHBKyyf0Q==", "signatures": [{"sig": "MEUCIDWwEQszdYj7yv3mUoKPGGDSgHhAkdbKq3WYiL6Q5b1zAiEAvqXoQNoFKF/pTzsEhxz3DzLXpOW9cxyqsM2NVobppy8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18228}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "34502b83cbc768d1a4fc68fe3f4a1eb21d15717f", "private": false, "scripts": {"build": "tsc && node -e \"require('fs').chmodSync('build/index.js', '755')\"", "watch": "tsc --watch", "prepare": "npm run build", "inspector": "npx @modelcontextprotocol/inspector build/index.js"}, "_npmUser": {"name": "fatwang2", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/fatwang2/search1api-mcp.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "A Model Context Protocol (MCP) server that provides search and crawl functionality using Search1API", "directories": {}, "_nodeVersion": "23.1.0", "dependencies": {"axios": "^1.7.8", "dotenv": "^16.4.5", "@modelcontextprotocol/sdk": "0.6.0"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.3.3", "@types/node": "^20.11.24"}, "_npmOperationalInternal": {"tmp": "tmp/search1api-mcp_0.1.3_1733363671807_0.294858230259444", "host": "s3://npm-registry-packages"}}, "0.1.4": {"name": "search1api-mcp", "version": "0.1.4", "keywords": ["mcp", "claude", "search", "crawl", "google", "search1api"], "author": {"name": "fatwang2"}, "license": "MIT", "_id": "search1api-mcp@0.1.4", "maintainers": [{"name": "fatwang2", "email": "<EMAIL>"}], "homepage": "https://github.com/fatwang2/search1api-mcp#readme", "bugs": {"url": "https://github.com/fatwang2/search1api-mcp/issues"}, "bin": {"search1api-mcp": "build/index.js"}, "dist": {"shasum": "9ddb06a3f50b39d9c73e0f00a711f8be53728311", "tarball": "https://registry.npmjs.org/search1api-mcp/-/search1api-mcp-0.1.4.tgz", "fileCount": 5, "integrity": "sha512-tniiB6ca8w4+m2OaKEQzGcpFgsGFukvOP8aO2DLr4pn6sc8ebDQUgQZdiNARgUnZabrzun/PLnsJVWl2rOVhxw==", "signatures": [{"sig": "MEQCICKuKU/p7w4/dbpXhpQX1oG8hRn7sDtVsxXvgRnRiGpAAiA86um13OtMn6WFNNYN2YaabafAoFJ/UoaZYLdfW5HECw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 21366}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "4759aa432edf61ef1cc3df1f6d2d8901f1fd553f", "private": false, "scripts": {"build": "tsc && node -e \"require('fs').chmodSync('build/index.js', '755')\"", "watch": "tsc --watch", "prepare": "npm run build", "inspector": "npx @modelcontextprotocol/inspector build/index.js"}, "_npmUser": {"name": "fatwang2", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/fatwang2/search1api-mcp.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "A Model Context Protocol (MCP) server that provides search and crawl functionality using Search1API", "directories": {}, "_nodeVersion": "22.14.0", "dependencies": {"axios": "^1.7.8", "dotenv": "^16.4.5", "@modelcontextprotocol/sdk": "0.6.0"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.3.3", "@types/node": "^20.11.24"}, "_npmOperationalInternal": {"tmp": "tmp/search1api-mcp_0.1.4_1740131048169_0.2750596691432792", "host": "s3://npm-registry-packages-npm-production"}}, "0.1.5": {"name": "search1api-mcp", "version": "0.1.5", "keywords": ["mcp", "claude", "search", "crawl", "google", "search1api"], "author": {"name": "fatwang2"}, "license": "MIT", "_id": "search1api-mcp@0.1.5", "maintainers": [{"name": "fatwang2", "email": "<EMAIL>"}], "homepage": "https://github.com/fatwang2/search1api-mcp#readme", "bugs": {"url": "https://github.com/fatwang2/search1api-mcp/issues"}, "bin": {"search1api-mcp": "build/index.js"}, "dist": {"shasum": "653a6ccafc778846d2e76e1da30be0c563b3e9d0", "tarball": "https://registry.npmjs.org/search1api-mcp/-/search1api-mcp-0.1.5.tgz", "fileCount": 5, "integrity": "sha512-pFAmqRJ5I9spmG0dgk8eYG3ygF+1HYLzBEUuiOM59nQ19x1vMdRpJ6AsSM87dI2JDsUSLEsGKfA2WLDv7bCJxw==", "signatures": [{"sig": "MEYCIQCmOjyLiN3Woy+Gilko0r8z44enNO1/x/WzbHWhoJJkggIhAK+qjfhx6gfCkBt1PypemLyIQJ9Niaabgj7D1ezDcCWY", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 27409}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "9b9b4ff97127f7307d043a7217f050140dbe9e01", "private": false, "scripts": {"build": "tsc && node -e \"require('fs').chmodSync('build/index.js', '755')\"", "watch": "tsc --watch", "prepare": "npm run build", "inspector": "npx @modelcontextprotocol/inspector build/index.js"}, "_npmUser": {"name": "fatwang2", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/fatwang2/search1api-mcp.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "A Model Context Protocol (MCP) server that provides search and crawl functionality using Search1API", "directories": {}, "_nodeVersion": "22.14.0", "dependencies": {"axios": "^1.7.8", "dotenv": "^16.4.5", "@modelcontextprotocol/sdk": "0.6.0"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.3.3", "@types/node": "^20.11.24"}, "_npmOperationalInternal": {"tmp": "tmp/search1api-mcp_0.1.5_1742294694748_0.48492609922167107", "host": "s3://npm-registry-packages-npm-production"}}, "0.1.6": {"name": "search1api-mcp", "version": "0.1.6", "keywords": ["mcp", "claude", "search", "crawl", "google", "search1api"], "author": {"name": "fatwang2"}, "license": "MIT", "_id": "search1api-mcp@0.1.6", "maintainers": [{"name": "fatwang2", "email": "<EMAIL>"}], "homepage": "https://github.com/fatwang2/search1api-mcp#readme", "bugs": {"url": "https://github.com/fatwang2/search1api-mcp/issues"}, "bin": {"search1api-mcp": "build/index.js"}, "dist": {"shasum": "8e15caa4895d602d7f0b0798951001b39f7de5eb", "tarball": "https://registry.npmjs.org/search1api-mcp/-/search1api-mcp-0.1.6.tgz", "fileCount": 5, "integrity": "sha512-L3OtQq2ZvAp3rF3uOpksgKGNdfTlQph3QOa5IOd19PuOrgd51z1FcyY6pZmS1G2VdbvBL8WR7pZa1mWxXoEPug==", "signatures": [{"sig": "MEUCIFkxb1prx7iQ9GK3wPV/jOKaYJFPZxFcS49mb54lyDblAiEA5H/lcCJWjNsq15YemHAAUU+KzO8kivljsicEcIqY+rY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 26241}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "e1b3c65e6a27184e84a82b8e99b46f84cb34843a", "private": false, "scripts": {"build": "tsc && node -e \"require('fs').chmodSync('build/index.js', '755')\"", "watch": "tsc --watch", "prepare": "npm run build", "inspector": "npx @modelcontextprotocol/inspector build/index.js"}, "_npmUser": {"name": "fatwang2", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/fatwang2/search1api-mcp.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "A Model Context Protocol (MCP) server that provides search and crawl functionality using Search1API", "directories": {}, "_nodeVersion": "22.14.0", "dependencies": {"axios": "^1.7.8", "dotenv": "^16.4.5", "@modelcontextprotocol/sdk": "0.6.0"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.3.3", "@types/node": "^20.11.24"}, "_npmOperationalInternal": {"tmp": "tmp/search1api-mcp_0.1.6_1742452775874_0.08428896050696699", "host": "s3://npm-registry-packages-npm-production"}}, "0.1.7": {"name": "search1api-mcp", "version": "0.1.7", "keywords": ["mcp", "claude", "search", "crawl", "google", "search1api"], "author": {"name": "fatwang2"}, "license": "MIT", "_id": "search1api-mcp@0.1.7", "maintainers": [{"name": "fatwang2", "email": "<EMAIL>"}], "homepage": "https://github.com/fatwang2/search1api-mcp#readme", "bugs": {"url": "https://github.com/fatwang2/search1api-mcp/issues"}, "bin": {"search1api-mcp": "build/index.js"}, "dist": {"shasum": "79d6ee87135e42fc540f98e28459c101d5c87206", "tarball": "https://registry.npmjs.org/search1api-mcp/-/search1api-mcp-0.1.7.tgz", "fileCount": 18, "integrity": "sha512-ury/5vg0vZXrzdWnHABhNnhQf27S/berEXFhk7s67jdX9h59ew28Vhq0XaUce6NiA5OWp0Inxpr8dNmoFAfw0g==", "signatures": [{"sig": "MEYCIQCvnjDL+R3/DiwikXs2dromM3krsGb7dWLZL6s7MMVVZAIhAIZ8CHumxICuBSm5AasvgcBh5OYpijrUk4O8sX3nF6WE", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 34381}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "024fb3837eff40607fe84a0bf098fad7e0a4dfa8", "private": false, "scripts": {"build": "tsc && node -e \"require('fs').chmodSync('build/index.js', '755')\"", "watch": "tsc --watch", "prepare": "npm run build", "inspector": "npx @modelcontextprotocol/inspector build/index.js"}, "_npmUser": {"name": "fatwang2", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/fatwang2/search1api-mcp.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "A Model Context Protocol (MCP) server that provides search and crawl functionality using Search1API", "directories": {}, "_nodeVersion": "22.14.0", "dependencies": {"axios": "^1.7.8", "dotenv": "^16.4.5", "@modelcontextprotocol/sdk": "^1.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"typescript": "^5.3.3", "@types/node": "^20.11.24"}, "_npmOperationalInternal": {"tmp": "tmp/search1api-mcp_0.1.7_1742546599905_0.09064917787513949", "host": "s3://npm-registry-packages-npm-production"}}, "0.1.8": {"name": "search1api-mcp", "version": "0.1.8", "description": "A Model Context Protocol (MCP) server that provides search and crawl functionality using Search1API", "private": false, "type": "module", "bin": {"search1api-mcp": "build/index.js"}, "scripts": {"build": "tsc && node -e \"require('fs').chmodSync('build/index.js', '755')\"", "prepare": "npm run build", "watch": "tsc --watch", "inspector": "npx @modelcontextprotocol/inspector build/index.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.7.0", "axios": "^1.7.8", "dotenv": "^16.4.5"}, "devDependencies": {"@types/node": "^20.11.24", "typescript": "^5.3.3"}, "keywords": ["mcp", "claude", "search", "crawl", "google", "search1api"], "author": {"name": "fatwang2"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/fatwang2/search1api-mcp.git"}, "engines": {"node": ">=18.0.0"}, "_id": "search1api-mcp@0.1.8", "gitHead": "bcc591a06036679ff7eb752643ff99997471c76a", "bugs": {"url": "https://github.com/fatwang2/search1api-mcp/issues"}, "homepage": "https://github.com/fatwang2/search1api-mcp#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-OdYfze1xNdvgAVBHu75B2aemU3AgwEEE9z8JcluEFQ0G5R72CdoaEL3jGP5lldR/9Zfi6WV8tFewY9B62Xo49w==", "shasum": "9db4138d1b83472c98e82e4ad44a258b01f175c5", "tarball": "https://registry.npmjs.org/search1api-mcp/-/search1api-mcp-0.1.8.tgz", "fileCount": 18, "unpackedSize": 34597, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCHaznW6dclJdlHzyEgtKk79DMaVOF2QEA/4ViZtwJf1QIgDeypEdTWdWN1zrbef+Y+TKtFtCyc0ylF2TFTWdcR5QU="}]}, "_npmUser": {"name": "fatwang2", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fatwang2", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/search1api-mcp_0.1.8_1743169739335_0.31216940051260256"}, "_hasShrinkwrap": false}}, "time": {"created": "2024-11-26T08:54:46.251Z", "modified": "2025-03-28T13:48:59.766Z", "0.1.0": "2024-11-26T08:54:46.408Z", "0.1.1": "2024-11-26T10:34:05.913Z", "0.1.2": "2024-11-29T00:37:41.838Z", "0.1.3": "2024-12-05T01:54:31.982Z", "0.1.4": "2025-02-21T09:44:08.449Z", "0.1.5": "2025-03-18T10:44:54.926Z", "0.1.6": "2025-03-20T06:39:36.060Z", "0.1.7": "2025-03-21T08:43:20.104Z", "0.1.8": "2025-03-28T13:48:59.617Z"}, "bugs": {"url": "https://github.com/fatwang2/search1api-mcp/issues"}, "author": {"name": "fatwang2"}, "license": "MIT", "homepage": "https://github.com/fatwang2/search1api-mcp#readme", "keywords": ["mcp", "claude", "search", "crawl", "google", "search1api"], "repository": {"type": "git", "url": "git+https://github.com/fatwang2/search1api-mcp.git"}, "description": "A Model Context Protocol (MCP) server that provides search and crawl functionality using Search1API", "maintainers": [{"name": "fatwang2", "email": "<EMAIL>"}], "readme": "# Search1API MCP Server\n\n[中文文档](./README_zh.md)\n\nA Model Context Protocol (MCP) server that provides search and crawl functionality using Search1API.\n\nhttps://github.com/user-attachments/assets/58bc98ae-3b6b-442c-a7fc-010508b5f028\n\nMore discussions and updates, please follow our official [X](https://x.com/search1api), or join the official [discord](https://discord.com/invite/AKXYq32Bxc)\n\n## Features\n\n- Web search functionality\n- News search functionality\n- Web page content extraction\n- Website sitemap extraction\n- Deep thinking and complex problem solving with DeepSeek R1\n- Seamless integration with Claude <PERSON>op, Cursor, Windsurf, Cline and other MCP clients\n\n## Tools\n\n### 1. Search Tool\n- Name: `search`\n- Description: Search the web using Search1API\n- Parameters:\n  * `query` (required): Search query in natural language. Be specific and concise for better results\n  * `max_results` (optional, default: 10): Number of results to return\n  * `search_service` (optional, default: \"google\"): Search service to use (google, bing, duckduckgo, yahoo, x, reddit, github, youtube, arxiv, wechat, bilibili, imdb, wikipedia)\n  * `crawl_results` (optional, default: 0): Number of results to crawl for full webpage content\n  * `include_sites` (optional): List of sites to include in search\n  * `exclude_sites` (optional): List of sites to exclude from search\n  * `time_range` (optional): Time range for search results (\"day\", \"month\", \"year\")\n\n### 2. News Tool\n- Name: `news`\n- Description: Search for news articles using Search1API\n- Parameters:\n  * `query` (required): Search query in natural language. Be specific and concise for better results\n  * `max_results` (optional, default: 10): Number of results to return\n  * `search_service` (optional, default: \"bing\"): Search service to use (google, bing, duckduckgo, yahoo, hackernews)\n  * `crawl_results` (optional, default: 0): Number of results to crawl for full webpage content\n  * `include_sites` (optional): List of sites to include in search\n  * `exclude_sites` (optional): List of sites to exclude from search\n  * `time_range` (optional): Time range for search results (\"day\", \"month\", \"year\")\n\n### 3. Crawl Tool\n- Name: `crawl`\n- Description: Extract content from a URL using Search1API\n- Parameters:\n  * `url` (required): URL to crawl\n\n### 4. Sitemap Tool\n- Name: `sitemap`\n- Description: Get all related links from a URL\n- Parameters:\n  * `url` (required): URL to get sitemap\n\n### 5. Reasoning Tool\n- Name: `reasoning`\n- Description: A tool for deep thinking and complex problem solving with fast deepseek r1 model and web search ability(You can change to any other model in search1api website but the speed is not guaranteed)\n- Parameters:\n  * `content` (required): The question or problem that needs deep thinking\n\n### 6. Trending Tool\n- Name: `trending`\n- Description: Get trending topics from popular platforms\n- Parameters:\n  * `search_service` (required): Specify the platform to get trending topics from (github, hackernews)\n  * `max_results` (optional, default: 10): Maximum number of trending items to return\n\n## Setup Guide\n\n### 1. Get Search1API Key\n1. Register at [Search1API](https://www.search1api.com/?utm_source=mcp)\n2. Get your api key and 100 free credits\n\n### 2. Configure \n\n```json\n{\n  \"mcpServers\": {\n    \"search1api\": {\n      \"command\": \"npx\",\n      \"args\": [\"-y\", \"search1api-mcp\"],\n      \"env\": {\n        \"SEARCH1API_KEY\": \"YOUR_SEARCH1API_KEY\"\n      }\n    }\n  }\n}\n```\n\n## Version History\n\n- v0.1.8: Added X(Twitter) and Reddit search services\n- v0.1.7: Added Trending tool for GitHub and Hacker News\n- v0.1.6: Added Wikipedia search service\n- v0.1.5: Added new search parameters (include_sites, exclude_sites, time_range) and new search services (arxiv, wechat, bilibili, imdb)\n- v0.1.4: Added reasoning tool with deepseek r1 and updated the Cursor and Windsurf configuration guide\n- v0.1.3: Added news search functionality\n- v0.1.2: Added sitemap functionality\n- v0.1.1: Added web crawling functionality\n- v0.1.0: Initial release with search functionality\n\n## License\n\nThis project is licensed under the MIT License - see the LICENSE file for details.\n", "readmeFilename": "README.md"}