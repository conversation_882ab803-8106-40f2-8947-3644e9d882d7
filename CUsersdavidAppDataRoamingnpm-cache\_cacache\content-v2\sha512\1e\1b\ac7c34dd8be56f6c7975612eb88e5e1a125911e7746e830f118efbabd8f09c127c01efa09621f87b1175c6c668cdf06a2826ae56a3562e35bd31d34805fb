{"_id": "@modelcontextprotocol/server-filesystem", "_rev": "10-b3322213fe5d3ff715a4fef60417be1f", "name": "@modelcontextprotocol/server-filesystem", "dist-tags": {"latest": "2025.7.1"}, "versions": {"0.2.0": {"name": "@modelcontextprotocol/server-filesystem", "version": "0.2.0", "author": {"url": "https://anthropic.com", "name": "Anthropic, PBC"}, "license": "MIT", "_id": "@modelcontextprotocol/server-filesystem@0.2.0", "maintainers": [{"name": "ashwin-ant", "email": "<EMAIL>"}, {"name": "thedsp", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}], "homepage": "https://modelcontextprotocol.io", "bugs": {"url": "https://github.com/modelcontextprotocol/servers/issues"}, "bin": {"mcp-server-filesystem": "dist/index.js"}, "dist": {"shasum": "ad72e266073efdd39898231aee64cadbd583923a", "tarball": "https://registry.npmjs.org/@modelcontextprotocol/server-filesystem/-/server-filesystem-0.2.0.tgz", "fileCount": 3, "integrity": "sha512-brBfDa8DzMK868DY5yITy4WTWkqLhpsFBr6n8ffO2VJXvDlPydWgyP6KapGtOzpNISjKeXk48nguGhHdTAp2Zg==", "signatures": [{"sig": "MEUCIAEDkTEQyLmDGcOk1B9R6uQU7vayt4iIY6zJDG4j7PWDAiEAlO+RUII6bGaGJfrpCa//DOiZ/CzG0XQPsqSAWWypjt0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19645}, "type": "module", "gitHead": "3d6de8673069bbb5d0418cd0db9b2fd81a53769d", "scripts": {"build": "tsc && shx chmod +x dist/*.js", "watch": "tsc --watch", "prepare": "npm run build"}, "_npmUser": {"name": "jspahrsummers", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "MCP server for filesystem access", "directories": {}, "_nodeVersion": "18.20.5", "dependencies": {"glob": "^10.3.10", "@modelcontextprotocol/sdk": "0.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"shx": "^0.3.4", "typescript": "^5.3.3", "@types/node": "^20.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/server-filesystem_0.2.0_1732216494461_0.16172954449997468", "host": "s3://npm-registry-packages"}}, "0.3.0": {"name": "@modelcontextprotocol/server-filesystem", "version": "0.3.0", "author": {"url": "https://anthropic.com", "name": "Anthropic, PBC"}, "license": "MIT", "_id": "@modelcontextprotocol/server-filesystem@0.3.0", "maintainers": [{"name": "ashwin-ant", "email": "<EMAIL>"}, {"name": "thedsp", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}], "homepage": "https://modelcontextprotocol.io", "bugs": {"url": "https://github.com/modelcontextprotocol/servers/issues"}, "bin": {"mcp-server-filesystem": "dist/index.js"}, "dist": {"shasum": "7492148fe76fa3fc507ac296c5ef0abab58e64cf", "tarball": "https://registry.npmjs.org/@modelcontextprotocol/server-filesystem/-/server-filesystem-0.3.0.tgz", "fileCount": 3, "integrity": "sha512-gIq6Lvsd0R8GZSyChpJBRmL9HLEtbjmQWzHiOKGWLt8TCA5z+/133n+/d6ym/1huwiWm0rdERiynHIyngjGHNA==", "signatures": [{"sig": "MEYCIQCWb/mhSQlhZA9D+TXImyuuZpoAHoWJix8yaJPZcDoJlAIhAL4Wks892DeM1Pk8ziBnMeER3VKaDpFY0gl3WRmNUxTd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19666}, "type": "module", "gitHead": "fe4f568fd011f9cbc8b7501e10117b7106bea84f", "scripts": {"build": "tsc && shx chmod +x dist/*.js", "watch": "tsc --watch", "prepare": "npm run build"}, "_npmUser": {"name": "jspahrsummers", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "MCP server for filesystem access", "directories": {}, "_nodeVersion": "18.20.5", "dependencies": {"glob": "^10.3.10", "@modelcontextprotocol/sdk": "0.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"shx": "^0.3.4", "typescript": "^5.3.3", "@types/node": "^20.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/server-filesystem_0.3.0_1732228636266_0.7065280222104064", "host": "s3://npm-registry-packages"}}, "0.5.0": {"name": "@modelcontextprotocol/server-filesystem", "version": "0.5.0", "author": {"url": "https://anthropic.com", "name": "Anthropic, PBC"}, "license": "MIT", "_id": "@modelcontextprotocol/server-filesystem@0.5.0", "maintainers": [{"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "thedsp", "email": "<EMAIL>"}, {"name": "ashwin-ant", "email": "<EMAIL>"}], "homepage": "https://modelcontextprotocol.io", "bugs": {"url": "https://github.com/modelcontextprotocol/servers/issues"}, "bin": {"mcp-server-filesystem": "dist/index.js"}, "dist": {"shasum": "1f5872f7fd6c538164bb224a8de7a63c613e283f", "tarball": "https://registry.npmjs.org/@modelcontextprotocol/server-filesystem/-/server-filesystem-0.5.0.tgz", "fileCount": 3, "integrity": "sha512-CVZrXhYu6VkNijrssjdeqSWvIYa2W612tlgIU/YjRD2ae1KOIvGM293lBALPh1O81xHtaU3vfiaP8gdE0Lsw6A==", "signatures": [{"sig": "MEQCICuUWcsqQVKvBbL2VCy/0XyujKngJaXGaKUE5s/oC+z9AiBiqwkJTidzIOjPhNo4QYIfoJjmp41AGePdLgy9g/MxKw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19932}, "type": "module", "gitHead": "45c4f70da468e2d81b9efe0d736b49efde1b9263", "scripts": {"build": "tsc && shx chmod +x dist/*.js", "watch": "tsc --watch", "prepare": "npm run build"}, "_npmUser": {"name": "jspahrsummers", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "MCP server for filesystem access", "directories": {}, "_nodeVersion": "18.20.5", "dependencies": {"glob": "^10.3.10", "@modelcontextprotocol/sdk": "0.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"shx": "^0.3.4", "typescript": "^5.3.3", "@types/node": "^20.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/server-filesystem_0.5.0_1732540612928_0.9524468336967082", "host": "s3://npm-registry-packages"}}, "0.5.1": {"name": "@modelcontextprotocol/server-filesystem", "version": "0.5.1", "author": {"url": "https://anthropic.com", "name": "Anthropic, PBC"}, "license": "MIT", "_id": "@modelcontextprotocol/server-filesystem@0.5.1", "maintainers": [{"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "thedsp", "email": "<EMAIL>"}, {"name": "ashwin-ant", "email": "<EMAIL>"}], "homepage": "https://modelcontextprotocol.io", "bugs": {"url": "https://github.com/modelcontextprotocol/servers/issues"}, "bin": {"mcp-server-filesystem": "dist/index.js"}, "dist": {"shasum": "0275e0d5e2c8d83520b5b74aaef6895e9c276e42", "tarball": "https://registry.npmjs.org/@modelcontextprotocol/server-filesystem/-/server-filesystem-0.5.1.tgz", "fileCount": 3, "integrity": "sha512-M<PERSON>+zgoDaEvoLsjej1b59W/u29QjFxrnYoV+gpQ+ge7q7olWisiWIpUnBvCtJ2nBfUMpX12OMb/I6ikrIb7+/TA==", "signatures": [{"sig": "MEYCIQC133scmHk2bNKjfjQsElDHSONmDaIUFbwHkg7gBBrL8gIhAPn4Y/72tnd2i+tdaNHzA6F7NPCgulFcZuE6qfxwSY8C", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19969}, "type": "module", "gitHead": "0da25bea8720c8162895f3222b46abb0c96c7a81", "scripts": {"build": "tsc && shx chmod +x dist/*.js", "watch": "tsc --watch", "prepare": "npm run build"}, "_npmUser": {"name": "jspahrsummers", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "MCP server for filesystem access", "directories": {}, "_nodeVersion": "18.20.5", "dependencies": {"glob": "^10.3.10", "zod-to-json-schema": "^3.23.5", "@modelcontextprotocol/sdk": "0.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"shx": "^0.3.4", "typescript": "^5.3.3", "@types/node": "^20.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/server-filesystem_0.5.1_1732564721530_0.1858968718991707", "host": "s3://npm-registry-packages"}}, "0.6.0": {"name": "@modelcontextprotocol/server-filesystem", "version": "0.6.0", "author": {"url": "https://anthropic.com", "name": "Anthropic, PBC"}, "license": "MIT", "_id": "@modelcontextprotocol/server-filesystem@0.6.0", "maintainers": [{"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "thedsp", "email": "<EMAIL>"}, {"name": "ashwin-ant", "email": "<EMAIL>"}], "homepage": "https://modelcontextprotocol.io", "bugs": {"url": "https://github.com/modelcontextprotocol/servers/issues"}, "bin": {"mcp-server-filesystem": "dist/index.js"}, "dist": {"shasum": "1dd85bc02646c0d61989c9491a6ca1d69d48cb46", "tarball": "https://registry.npmjs.org/@modelcontextprotocol/server-filesystem/-/server-filesystem-0.6.0.tgz", "fileCount": 3, "integrity": "sha512-x5cRCItR4xuRbEU9aLFYCK1n04tjvpzKsqAjT+RQeyNzeuIoPI6W+ZPLEe3DQiLQgefGwMb991w8FymRkg4SHg==", "signatures": [{"sig": "MEUCIFuWc878onGwVsYjCSNbbLCfDKgQjCH9aagtZcnXgAPBAiEAnckHe2IOQG+Q83wxluQmZWhHbfG6mk/h6eojIRrgJZo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20039}, "type": "module", "gitHead": "129d80af313c6c0ad9a929f4923c6d8a07d6a9e5", "scripts": {"build": "tsc && shx chmod +x dist/*.js", "watch": "tsc --watch", "prepare": "npm run build"}, "_npmUser": {"name": "jspahrsummers", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "MCP server for filesystem access", "directories": {}, "_nodeVersion": "18.20.5", "dependencies": {"glob": "^10.3.10", "zod-to-json-schema": "^3.23.5", "@modelcontextprotocol/sdk": "1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"shx": "^0.3.4", "typescript": "^5.3.3", "@types/node": "^20.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/server-filesystem_0.6.0_1733237399118_0.35227984229316944", "host": "s3://npm-registry-packages"}}, "0.6.1": {"name": "@modelcontextprotocol/server-filesystem", "version": "0.6.1", "author": {"url": "https://anthropic.com", "name": "Anthropic, PBC"}, "license": "MIT", "_id": "@modelcontextprotocol/server-filesystem@0.6.1", "maintainers": [{"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "thedsp", "email": "<EMAIL>"}, {"name": "ashwin-ant", "email": "<EMAIL>"}], "homepage": "https://modelcontextprotocol.io", "bugs": {"url": "https://github.com/modelcontextprotocol/servers/issues"}, "bin": {"mcp-server-filesystem": "dist/index.js"}, "dist": {"shasum": "3703bee90b8e652eacf2bb5a54d7637c1bab7b87", "tarball": "https://registry.npmjs.org/@modelcontextprotocol/server-filesystem/-/server-filesystem-0.6.1.tgz", "fileCount": 3, "integrity": "sha512-7tVqEs4qR4uZahzQmZZ7V7H3prq99vVFeTMvRQPTu1Z2h/MjhdGOSRPru3Fh+8dZVNxNpcoxXbVM5FBtWQaP5g==", "signatures": [{"sig": "MEUCIQDXD8EqmXbuJ7R0wBUcfpAwcByuXhmgcKwpCFKSEEKRrgIgBo6XIWvowFtwomQRvs1FVH1jqcd05AM2y2TXdjJa+0c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20039}, "type": "module", "gitHead": "a096c95e8eaa718b0efbce64ee77e2ddda4fdcd8", "scripts": {"build": "tsc && shx chmod +x dist/*.js", "watch": "tsc --watch", "prepare": "npm run build"}, "_npmUser": {"name": "jspahrsummers", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "MCP server for filesystem access", "directories": {}, "_nodeVersion": "18.20.5", "dependencies": {"glob": "^10.3.10", "zod-to-json-schema": "^3.23.5", "@modelcontextprotocol/sdk": "1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"shx": "^0.3.4", "typescript": "^5.3.3", "@types/node": "^20.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/server-filesystem_0.6.1_1733248347692_0.11963703607566445", "host": "s3://npm-registry-packages"}}, "0.6.2": {"name": "@modelcontextprotocol/server-filesystem", "version": "0.6.2", "author": {"url": "https://anthropic.com", "name": "Anthropic, PBC"}, "license": "MIT", "_id": "@modelcontextprotocol/server-filesystem@0.6.2", "maintainers": [{"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "thedsp", "email": "<EMAIL>"}, {"name": "ashwin-ant", "email": "<EMAIL>"}], "homepage": "https://modelcontextprotocol.io", "bugs": {"url": "https://github.com/modelcontextprotocol/servers/issues"}, "bin": {"mcp-server-filesystem": "dist/index.js"}, "dist": {"shasum": "99fea4dcdb540f88544d8f4d7037916fc6f1ac27", "tarball": "https://registry.npmjs.org/@modelcontextprotocol/server-filesystem/-/server-filesystem-0.6.2.tgz", "fileCount": 3, "integrity": "sha512-qBrhLY524WEFmIg+s2O6bPIFBK8Dy0l20yjQ0reYN1moWYNy28kNyYgWVgTiSj4QvpMq2LFZs6foDHrG1Kgt2w==", "signatures": [{"sig": "MEUCIB5u8T0Rw91f12bMMvaPTxI4zXVZVbMCHi6ueSCMmsaEAiEAns1YnFvuCXn5gRhO4jccO+L/NLFxDUwT4OgRBsB2U9s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20038}, "type": "module", "gitHead": "94a36286d2ea49d095704167846283f0c2c2d5d1", "scripts": {"build": "tsc && shx chmod +x dist/*.js", "watch": "tsc --watch", "prepare": "npm run build"}, "_npmUser": {"name": "jspahrsummers", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "MCP server for filesystem access", "directories": {}, "_nodeVersion": "18.20.5", "dependencies": {"glob": "^10.3.10", "zod-to-json-schema": "^3.23.5", "@modelcontextprotocol/sdk": "1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"shx": "^0.3.4", "typescript": "^5.3.3", "@types/node": "^20.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/server-filesystem_0.6.2_1733328894933_0.029322889486741888", "host": "s3://npm-registry-packages"}}, "2025.1.14": {"name": "@modelcontextprotocol/server-filesystem", "version": "2025.1.14", "author": {"url": "https://anthropic.com", "name": "Anthropic, PBC"}, "license": "MIT", "_id": "@modelcontextprotocol/server-filesystem@2025.1.14", "maintainers": [{"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "thedsp", "email": "<EMAIL>"}, {"name": "ashwin-ant", "email": "<EMAIL>"}], "homepage": "https://modelcontextprotocol.io", "bugs": {"url": "https://github.com/modelcontextprotocol/servers/issues"}, "bin": {"mcp-server-filesystem": "dist/index.js"}, "dist": {"shasum": "ecad503f9e8e56e4be1579989164f97c0e3fbb6d", "tarball": "https://registry.npmjs.org/@modelcontextprotocol/server-filesystem/-/server-filesystem-2025.1.14.tgz", "fileCount": 3, "integrity": "sha512-WjJYa2OAAf0fpslyxjzJK3JUeEk10IaDukLOc4NSoJtNptvJONd4NMC6U3Xr04VOb++2iNfM7Vp6Ac7o94Pv6g==", "signatures": [{"sig": "MEUCIQDWTg3ElaKpyM+WywnVvWR1YU0lyqJpfY78nVIxlBjK6gIgG1C07h4dH5XyCN7xKaRxuKueX8ZkgrJ1L9XnI+YlqfU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29855}, "type": "module", "gitHead": "ea73a074e8d2b4c424429c7f364dae558211ff30", "scripts": {"build": "tsc && shx chmod +x dist/*.js", "watch": "tsc --watch", "prepare": "npm run build"}, "_npmUser": {"name": "jspahrsummers", "email": "<EMAIL>"}, "_npmVersion": "10.9.0", "description": "MCP server for filesystem access", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"diff": "^5.1.0", "glob": "^10.3.10", "minimatch": "^10.0.1", "zod-to-json-schema": "^3.23.5", "@modelcontextprotocol/sdk": "0.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"shx": "^0.3.4", "typescript": "^5.3.3", "@types/diff": "^5.0.9", "@types/node": "^22", "@types/minimatch": "^5.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/server-filesystem_2025.1.14_1736819066200_0.8197785477491994", "host": "s3://npm-registry-packages-npm-production"}}, "2025.3.28": {"name": "@modelcontextprotocol/server-filesystem", "version": "2025.3.28", "author": {"url": "https://anthropic.com", "name": "Anthropic, PBC"}, "license": "MIT", "_id": "@modelcontextprotocol/server-filesystem@2025.3.28", "maintainers": [{"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "thedsp", "email": "<EMAIL>"}, {"name": "ashwin-ant", "email": "<EMAIL>"}], "homepage": "https://modelcontextprotocol.io", "bugs": {"url": "https://github.com/modelcontextprotocol/servers/issues"}, "bin": {"mcp-server-filesystem": "dist/index.js"}, "dist": {"shasum": "a59a18a9320cecd3dd507d17e16d7b01bb233d8b", "tarball": "https://registry.npmjs.org/@modelcontextprotocol/server-filesystem/-/server-filesystem-2025.3.28.tgz", "fileCount": 3, "integrity": "sha512-1AMqM0EZnF7n6L5njMASDR12ppyvtj89HinePbvB8UtT5JKWQ6LJJcbsTYIt/gerFNssf17gH5qXvqSM+eCQSg==", "signatures": [{"sig": "MEYCIQCssX9ESnXgWl+tjdhh2odTwliadQughpBXBpCE0TXWbwIhAJilK3NVCdT0fMJMX5/Tm5pPktW+gus+0gTadCFb+SMF", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 29458}, "type": "module", "gitHead": "e5ed5bff23c01fab9976b7e3cc9446411b9a23ce", "scripts": {"build": "tsc && shx chmod +x dist/*.js", "watch": "tsc --watch", "prepare": "npm run build"}, "_npmUser": {"name": "jspahrsummers", "email": "<EMAIL>"}, "_npmVersion": "10.9.2", "description": "MCP server for filesystem access", "directories": {}, "_nodeVersion": "22.14.0", "dependencies": {"diff": "^5.1.0", "glob": "^10.3.10", "minimatch": "^10.0.1", "zod-to-json-schema": "^3.23.5", "@modelcontextprotocol/sdk": "0.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"shx": "^0.3.4", "typescript": "^5.3.3", "@types/diff": "^5.0.9", "@types/node": "^22", "@types/minimatch": "^5.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/server-filesystem_2025.3.28_1743158476316_0.0007114808038370946", "host": "s3://npm-registry-packages-npm-production"}}, "2025.7.1": {"name": "@modelcontextprotocol/server-filesystem", "version": "2025.7.1", "description": "MCP server for filesystem access", "license": "MIT", "author": {"name": "Anthropic, PBC", "url": "https://anthropic.com"}, "homepage": "https://modelcontextprotocol.io", "bugs": {"url": "https://github.com/modelcontextprotocol/servers/issues"}, "type": "module", "bin": {"mcp-server-filesystem": "dist/index.js"}, "scripts": {"build": "tsc && shx chmod +x dist/*.js", "prepare": "npm run build", "watch": "tsc --watch", "test": "jest --config=jest.config.cjs --coverage"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.12.3", "diff": "^5.1.0", "glob": "^10.3.10", "minimatch": "^10.0.1", "zod-to-json-schema": "^3.23.5"}, "devDependencies": {"@jest/globals": "^29.7.0", "@types/diff": "^5.0.9", "@types/jest": "^29.5.14", "@types/minimatch": "^5.1.2", "@types/node": "^22", "jest": "^29.7.0", "shx": "^0.3.4", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "^5.8.2"}, "_id": "@modelcontextprotocol/server-filesystem@2025.7.1", "gitHead": "8a7ad88e5c6f71211bd4feb15bcb563fb758412b", "_nodeVersion": "22.16.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-mGXxYX8l4EWQPgh/1pLZI2baKifPy4+WUx1jJ572TsCbe/Bs7S8f7z0Dp9aRlpcLEMr/Qp+Rp1m3NXNsgdKUmg==", "shasum": "83e54560fead3850803443a9d646aabe95b38aa8", "tarball": "https://registry.npmjs.org/@modelcontextprotocol/server-filesystem/-/server-filesystem-2025.7.1.tgz", "fileCount": 7, "unpackedSize": 98023, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIE4Mdp8koh7ZNId8KZQaphyk4dAgT73qwUh0vVOorG8nAiEA2OL66za3vCb3Ys2YRKEiimP632ix41q2p/mPxofmAzU="}]}, "_npmUser": {"name": "jspahrsummers", "email": "<EMAIL>", "actor": {"name": "jspahrsummers", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "thedsp", "email": "<EMAIL>"}, {"name": "ashwin-ant", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/server-filesystem_2025.7.1_1751394730005_0.32728788770983197"}, "_hasShrinkwrap": false}}, "time": {"created": "2024-11-21T19:14:54.330Z", "modified": "2025-07-01T18:32:10.465Z", "0.2.0": "2024-11-21T19:14:54.639Z", "0.3.0": "2024-11-21T22:37:16.413Z", "0.5.0": "2024-11-25T13:16:53.187Z", "0.5.1": "2024-11-25T19:58:41.722Z", "0.6.0": "2024-12-03T14:49:59.272Z", "0.6.1": "2024-12-03T17:52:27.868Z", "0.6.2": "2024-12-04T16:14:55.116Z", "2025.1.14": "2025-01-14T01:44:26.378Z", "2025.3.28": "2025-03-28T10:41:16.528Z", "2025.7.1": "2025-07-01T18:32:10.176Z"}, "bugs": {"url": "https://github.com/modelcontextprotocol/servers/issues"}, "author": {"name": "Anthropic, PBC", "url": "https://anthropic.com"}, "license": "MIT", "homepage": "https://modelcontextprotocol.io", "description": "MCP server for filesystem access", "maintainers": [{"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "thedsp", "email": "<EMAIL>"}, {"name": "ashwin-ant", "email": "<EMAIL>"}], "readme": "# Filesystem MCP Server\n\nNode.js server implementing Model Context Protocol (MCP) for filesystem operations.\n\n## Features\n\n- Read/write files\n- Create/list/delete directories\n- Move files/directories\n- Search files\n- Get file metadata\n\n**Note**: The server will only allow operations within directories specified via `args`.\n\n## API\n\n### Resources\n\n- `file://system`: File system operations interface\n\n### Tools\n\n- **read_file**\n  - Read complete contents of a file\n  - Input: `path` (string)\n  - Reads complete file contents with UTF-8 encoding\n\n- **read_multiple_files**\n  - Read multiple files simultaneously\n  - Input: `paths` (string[])\n  - Failed reads won't stop the entire operation\n\n- **write_file**\n  - Create new file or overwrite existing (exercise caution with this)\n  - Inputs:\n    - `path` (string): File location\n    - `content` (string): File content\n\n- **edit_file**\n  - Make selective edits using advanced pattern matching and formatting\n  - Features:\n    - Line-based and multi-line content matching\n    - Whitespace normalization with indentation preservation\n    - Multiple simultaneous edits with correct positioning\n    - Indentation style detection and preservation\n    - Git-style diff output with context\n    - Preview changes with dry run mode\n  - Inputs:\n    - `path` (string): File to edit\n    - `edits` (array): List of edit operations\n      - `oldText` (string): Text to search for (can be substring)\n      - `newText` (string): Text to replace with\n    - `dryRun` (boolean): Preview changes without applying (default: false)\n  - Returns detailed diff and match information for dry runs, otherwise applies changes\n  - Best Practice: Always use dryRun first to preview changes before applying them\n\n- **create_directory**\n  - Create new directory or ensure it exists\n  - Input: `path` (string)\n  - Creates parent directories if needed\n  - Succeeds silently if directory exists\n\n- **list_directory**\n  - List directory contents with [FILE] or [DIR] prefixes\n  - Input: `path` (string)\n\n- **move_file**\n  - Move or rename files and directories\n  - Inputs:\n    - `source` (string)\n    - `destination` (string)\n  - Fails if destination exists\n\n- **search_files**\n  - Recursively search for files/directories\n  - Inputs:\n    - `path` (string): Starting directory\n    - `pattern` (string): Search pattern\n    - `excludePatterns` (string[]): Exclude any patterns. Glob formats are supported.\n  - Case-insensitive matching\n  - Returns full paths to matches\n\n- **get_file_info**\n  - Get detailed file/directory metadata\n  - Input: `path` (string)\n  - Returns:\n    - Size\n    - Creation time\n    - Modified time\n    - Access time\n    - Type (file/directory)\n    - Permissions\n\n- **list_allowed_directories**\n  - List all directories the server is allowed to access\n  - No input required\n  - Returns:\n    - Directories that this server can read/write from\n\n## Usage with Claude Desktop\nAdd this to your `claude_desktop_config.json`:\n\nNote: you can provide sandboxed directories to the server by mounting them to `/projects`. Adding the `ro` flag will make the directory readonly by the server.\n\n### Docker\nNote: all directories must be mounted to `/projects` by default.\n\n```json\n{\n  \"mcpServers\": {\n    \"filesystem\": {\n      \"command\": \"docker\",\n      \"args\": [\n        \"run\",\n        \"-i\",\n        \"--rm\",\n        \"--mount\", \"type=bind,src=/Users/<USER>/Desktop,dst=/projects/Desktop\",\n        \"--mount\", \"type=bind,src=/path/to/other/allowed/dir,dst=/projects/other/allowed/dir,ro\",\n        \"--mount\", \"type=bind,src=/path/to/file.txt,dst=/projects/path/to/file.txt\",\n        \"mcp/filesystem\",\n        \"/projects\"\n      ]\n    }\n  }\n}\n```\n\n### NPX\n\n```json\n{\n  \"mcpServers\": {\n    \"filesystem\": {\n      \"command\": \"npx\",\n      \"args\": [\n        \"-y\",\n        \"@modelcontextprotocol/server-filesystem\",\n        \"/Users/<USER>/Desktop\",\n        \"/path/to/other/allowed/dir\"\n      ]\n    }\n  }\n}\n```\n\n## Usage with VS Code\n\nFor quick installation, click the installation buttons below...\n\n[![Install with NPX in VS Code](https://img.shields.io/badge/VS_Code-NPM-0098FF?style=flat-square&logo=visualstudiocode&logoColor=white)](https://insiders.vscode.dev/redirect/mcp/install?name=filesystem&config=%7B%22command%22%3A%22npx%22%2C%22args%22%3A%5B%22-y%22%2C%22%40modelcontextprotocol%2Fserver-filesystem%22%2C%22%24%7BworkspaceFolder%7D%22%5D%7D) [![Install with NPX in VS Code Insiders](https://img.shields.io/badge/VS_Code_Insiders-NPM-24bfa5?style=flat-square&logo=visualstudiocode&logoColor=white)](https://insiders.vscode.dev/redirect/mcp/install?name=filesystem&config=%7B%22command%22%3A%22npx%22%2C%22args%22%3A%5B%22-y%22%2C%22%40modelcontextprotocol%2Fserver-filesystem%22%2C%22%24%7BworkspaceFolder%7D%22%5D%7D&quality=insiders)\n\n[![Install with Docker in VS Code](https://img.shields.io/badge/VS_Code-Docker-0098FF?style=flat-square&logo=visualstudiocode&logoColor=white)](https://insiders.vscode.dev/redirect/mcp/install?name=filesystem&config=%7B%22command%22%3A%22docker%22%2C%22args%22%3A%5B%22run%22%2C%22-i%22%2C%22--rm%22%2C%22--mount%22%2C%22type%3Dbind%2Csrc%3D%24%7BworkspaceFolder%7D%2Cdst%3D%2Fprojects%2Fworkspace%22%2C%22mcp%2Ffilesystem%22%2C%22%2Fprojects%22%5D%7D) [![Install with Docker in VS Code Insiders](https://img.shields.io/badge/VS_Code_Insiders-Docker-24bfa5?style=flat-square&logo=visualstudiocode&logoColor=white)](https://insiders.vscode.dev/redirect/mcp/install?name=filesystem&config=%7B%22command%22%3A%22docker%22%2C%22args%22%3A%5B%22run%22%2C%22-i%22%2C%22--rm%22%2C%22--mount%22%2C%22type%3Dbind%2Csrc%3D%24%7BworkspaceFolder%7D%2Cdst%3D%2Fprojects%2Fworkspace%22%2C%22mcp%2Ffilesystem%22%2C%22%2Fprojects%22%5D%7D&quality=insiders)\n\nFor manual installation, add the following JSON block to your User Settings (JSON) file in VS Code. You can do this by pressing `Ctrl + Shift + P` and typing `Preferences: Open Settings (JSON)`.\n\nOptionally, you can add it to a file called `.vscode/mcp.json` in your workspace. This will allow you to share the configuration with others.\n\n> Note that the `mcp` key is not needed in the `.vscode/mcp.json` file.\n\nYou can provide sandboxed directories to the server by mounting them to `/projects`. Adding the `ro` flag will make the directory readonly by the server.\n\n### Docker\nNote: all directories must be mounted to `/projects` by default. \n\n```json\n{\n  \"mcp\": {\n    \"servers\": {\n      \"filesystem\": {\n        \"command\": \"docker\",\n        \"args\": [\n          \"run\",\n          \"-i\",\n          \"--rm\",\n          \"--mount\", \"type=bind,src=${workspaceFolder},dst=/projects/workspace\",\n          \"mcp/filesystem\",\n          \"/projects\"\n        ]\n      }\n    }\n  }\n}\n```\n\n### NPX\n\n```json\n{\n  \"mcp\": {\n    \"servers\": {\n      \"filesystem\": {\n        \"command\": \"npx\",\n        \"args\": [\n          \"-y\",\n          \"@modelcontextprotocol/server-filesystem\",\n          \"${workspaceFolder}\"\n        ]\n      }\n    }\n  }\n}\n```\n\n## Build\n\nDocker build:\n\n```bash\ndocker build -t mcp/filesystem -f src/filesystem/Dockerfile .\n```\n\n## License\n\nThis MCP server is licensed under the MIT License. This means you are free to use, modify, and distribute the software, subject to the terms and conditions of the MIT License. For more details, please see the LICENSE file in the project repository.\n", "readmeFilename": "README.md"}