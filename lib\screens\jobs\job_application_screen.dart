import 'package:flutter/material.dart';
import '../../design_system/app_theme.dart';
import '../../design_system/components/reusable_components.dart';

class JobApplicationScreen extends StatelessWidget {
  const JobApplicationScreen({super.key});

  static String routeName = 'job-application';
  static String routePath = '/jobs/job-application';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Job Application'),
        backgroundColor: AppTheme.primaryNavy,
        foregroundColor: AppTheme.white,
      ),
      body: const Padding(
        padding: EdgeInsets.all(AppTheme.spacingMd),
        child: Column(
          children: [
            JJE<PERSON>yState(
              title: 'Job Application',
              subtitle: 'Apply for electrical jobs and referrals',
              icon: Icons.work,
            ),
          ],
        ),
      ),
    );
  }
}
